@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS变量定义 */
:root {
  /* 主色调 - 浅色主题 */
  --color-primary-50: 230 247 255;
  --color-primary-100: 186 231 255;
  --color-primary-200: 145 213 255;
  --color-primary-300: 105 192 255;
  --color-primary-400: 64 169 255;
  --color-primary-500: 24 144 255;
  --color-primary-600: 9 109 217;
  --color-primary-700: 0 80 179;
  --color-primary-800: 0 58 140;
  --color-primary-900: 0 39 102;

  /* 灰色调 - 浅色主题 */
  --color-gray-50: 250 250 250;
  --color-gray-100: 245 245 245;
  --color-gray-200: 240 240 240;
  --color-gray-300: 217 217 217;
  --color-gray-400: 191 191 191;
  --color-gray-500: 140 140 140;
  --color-gray-600: 89 89 89;
  --color-gray-700: 67 67 67;
  --color-gray-800: 38 38 38;
  --color-gray-900: 31 31 31;

  /* 语义化颜色 - 浅色主题 */
  --color-background: 255 255 255;
  --color-foreground: 31 31 31;
  --color-surface: 250 250 250;
  --color-border: 240 240 240;
  --color-muted: 245 245 245;
  --color-accent: 24 144 255;
}

/* 暗色主题变量 */
.dark {
  /* 主色调 - 暗色主题 */
  --color-primary-50: 0 39 102;
  --color-primary-100: 0 58 140;
  --color-primary-200: 0 80 179;
  --color-primary-300: 9 109 217;
  --color-primary-400: 24 144 255;
  --color-primary-500: 64 169 255;
  --color-primary-600: 105 192 255;
  --color-primary-700: 145 213 255;
  --color-primary-800: 186 231 255;
  --color-primary-900: 230 247 255;

  /* 灰色调 - 暗色主题 */
  --color-gray-50: 31 31 31;
  --color-gray-100: 38 38 38;
  --color-gray-200: 67 67 67;
  --color-gray-300: 89 89 89;
  --color-gray-400: 140 140 140;
  --color-gray-500: 191 191 191;
  --color-gray-600: 217 217 217;
  --color-gray-700: 240 240 240;
  --color-gray-800: 245 245 245;
  --color-gray-900: 250 250 250;

  /* 语义化颜色 - 暗色主题 */
  --color-background: 0 0 0;
  --color-foreground: 250 250 250;
  --color-surface: 31 31 31;
  --color-border: 67 67 67;
  --color-muted: 38 38 38;
  --color-accent: 64 169 255;
}

/* 全局样式重置 */
@layer base {
  * {
    box-sizing: border-box;
  }

  html {
    font-size: 14px;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    background-color: #ffffff;
    color: #262626;
  }

  #root {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* 选择文本样式 */
  ::selection {
    background-color: #1890ff;
    color: white;
  }

  ::-moz-selection {
    background-color: #1890ff;
    color: white;
  }
}

/* 组件样式 */
@layer components {
  /* 按钮组件样式 */
  .btn-primary {
    @apply bg-primary-500 text-white px-4 py-2 rounded-md hover:bg-primary-600 transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-surface text-foreground px-4 py-2 rounded-md hover:bg-muted transition-colors duration-200;
  }

  /* 卡片组件样式 */
  .card {
    @apply bg-surface rounded-lg shadow-soft border border-border p-4;
  }

  .card-hover {
    @apply hover:shadow-medium transition-shadow duration-200;
  }

  /* 输入框样式 */
  .input-primary {
    @apply w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-background text-foreground;
  }

  /* 布局样式 */
  .layout-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .sidebar {
    @apply w-64 bg-surface border-r border-border flex-shrink-0;
  }

  .main-content {
    @apply flex-1 overflow-hidden;
  }
}

/* 工具类样式 */
@layer utilities {
  /* 文本省略 */
  .text-ellipsis {
    @apply truncate;
  }

  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 居中布局 */
  .center {
    @apply flex items-center justify-center;
  }

  .center-x {
    @apply flex justify-center;
  }

  .center-y {
    @apply flex items-center;
  }

  /* 间距工具 */
  .space-between {
    @apply flex items-center justify-between;
  }

  /* 隐藏滚动条 */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* 渐变背景 */
  .gradient-primary {
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  }

  .gradient-secondary {
    background: linear-gradient(135deg, #f0f2f5 0%, #ffffff 100%);
  }
}

/* 主题切换过渡动画 */
.theme-transitioning,
.theme-transitioning *,
.theme-transitioning *:before,
.theme-transitioning *:after {
  transition:
    background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    fill 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    stroke 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  transition-delay: 0s !important;
}

/* 防止过渡动画影响伪元素 */
.theme-transitioning *:before,
.theme-transitioning *:after {
  transition-property: background-color, color, border-color, box-shadow, opacity !important;
}

/* 特殊元素的过渡优化 */
.theme-transitioning .ant-layout,
.theme-transitioning .ant-layout-header,
.theme-transitioning .ant-layout-sider,
.theme-transitioning .ant-layout-content,
.theme-transitioning .ant-menu,
.theme-transitioning .ant-card,
.theme-transitioning .ant-btn,
.theme-transitioning .ant-input,
.theme-transitioning .ant-select {
  transition:
    background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 浅色主题 */
.light {
  color-scheme: light;
}

.light body {
  @apply bg-background text-foreground;
}

.light .card {
  @apply bg-surface border-border;
}

.light .sidebar {
  @apply bg-surface border-border;
}

.light .input-primary {
  @apply bg-background border-border text-foreground;
}

.light .input-primary:focus {
  @apply ring-primary-500 border-transparent;
}

/* 暗色主题 */
.dark {
  color-scheme: dark;
}

.dark body {
  @apply bg-background text-foreground;
}

.dark .card {
  @apply bg-surface border-border;
}

.dark .sidebar {
  @apply bg-surface border-border;
}

.dark .input-primary {
  @apply bg-background border-border text-foreground;
}

.dark .input-primary:focus {
  @apply ring-primary-400 border-transparent;
}

/* 主题切换按钮样式 */
.theme-toggle {
  @apply transition-all duration-300 ease-in-out;
  position: relative;
  overflow: hidden;
}

.theme-toggle:hover {
  @apply scale-105;
  transform: scale(1.05);
}

.theme-toggle:active {
  @apply scale-95;
  transform: scale(0.95);
}

.theme-toggle .anticon {
  @apply transition-all duration-300 ease-in-out;
  position: relative;
  z-index: 1;
}

.theme-toggle:hover .anticon {
  transform: rotate(15deg) scale(1.1);
}

/* 主题切换按钮的波纹效果 */
.theme-toggle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(24, 144, 255, 0.1);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
  z-index: 0;
}

.theme-toggle:hover::before {
  width: 100%;
  height: 100%;
}

/* 主题切换时的特殊动画 */
@keyframes themeSwitch {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.2);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

.theme-toggle.switching .anticon {
  animation: themeSwitch 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 暗色主题下的按钮样式调整 */
.dark .theme-toggle::before {
  background: rgba(64, 169, 255, 0.15);
}

.dark .theme-toggle:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.light .theme-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 搜索高亮样式 */
.search-highlight {
  background-color: #fff2e8;
  color: #d46b08;
  padding: 0 2px;
  border-radius: 2px;
  font-weight: 500;
}

.dark .search-highlight {
  background-color: #2d1b08;
  color: #ffbb96;
}

/* 搜索结果样式 */
.search-results-list .ant-list-item {
  border-bottom: 1px solid var(--color-border);
  padding: 16px 0;
}

.search-results-list .ant-list-item:hover {
  background-color: rgba(24, 144, 255, 0.02);
}

.dark .search-results-list .ant-list-item:hover {
  background-color: rgba(64, 169, 255, 0.05);
}

.search-result-item-card {
  border: none;
  box-shadow: none;
}

.search-result-item-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark .search-result-item-card:hover {
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
}

/* 搜索下拉框样式 */
.search-dropdown {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.search-dropdown-content {
  background: var(--color-background);
  border-radius: 8px;
}

.search-input .ant-input-group-addon {
  border: none;
  background: transparent;
}

.search-input .ant-btn {
  border-radius: 0 6px 6px 0;
}

/* 白板组件样式 */
.whiteboard-canvas {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--color-background);
}

.whiteboard-toolbar {
  padding: 16px;
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  border-radius: 8px 8px 0 0;
}

.whiteboard-canvas-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  background: #f5f5f5;
  overflow: auto;
}

.whiteboard-canvas-container canvas {
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.whiteboard-canvas.readonly {
  padding: 16px;
}

.whiteboard-canvas.readonly canvas {
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: #ffffff;
}

/* 暗色主题下的白板样式 */
.dark .whiteboard-canvas-container {
  background: #1a1a1a;
}

.dark .whiteboard-canvas-container canvas {
  background: #2a2a2a;
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
}

.dark .whiteboard-canvas.readonly canvas {
  background: #2a2a2a;
}

/* 工具栏按钮样式 */
.whiteboard-toolbar .ant-btn {
  transition: all 0.2s ease;
}

.whiteboard-toolbar .ant-btn:hover {
  transform: translateY(-1px);
}

.whiteboard-toolbar .ant-btn-primary {
  background: var(--color-primary-500);
  border-color: var(--color-primary-500);
}

/* 滑块样式 */
.whiteboard-toolbar .ant-slider {
  margin: 0;
}

.whiteboard-toolbar .ant-slider-track {
  background: var(--color-primary-500);
}

.whiteboard-toolbar .ant-slider-handle {
  border-color: var(--color-primary-500);
}

.whiteboard-toolbar .ant-slider-handle:hover {
  border-color: var(--color-primary-600);
}

.whiteboard-toolbar .ant-slider-handle:focus {
  border-color: var(--color-primary-600);
  box-shadow: 0 0 0 5px rgba(24, 144, 255, 0.12);
}

/* 思维导图组件样式 */
.mindmap-canvas {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--color-background);
}

.mindmap-toolbar {
  padding: 16px;
  background: var(--color-surface);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 8px;
}

.mindmap-toolbar .ant-btn {
  transition: all 0.2s ease;
}

.mindmap-toolbar .ant-btn:hover {
  transform: translateY(-1px);
}

.mindmap-toolbar .ant-btn-primary {
  background: var(--color-primary-500);
  border-color: var(--color-primary-500);
}

/* React Flow 样式覆盖 */
.react-flow__node {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.react-flow__node.selected {
  box-shadow: 0 0 0 2px var(--color-primary-500);
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: var(--color-primary-500);
  stroke-width: 3;
}

.react-flow__controls {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 8px;
}

.react-flow__controls-button {
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  color: var(--color-text);
}

.react-flow__controls-button:hover {
  background: var(--color-background);
}

.react-flow__minimap {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 8px;
}

/* 暗色主题下的思维导图样式 */
.dark .mindmap-toolbar {
  background: var(--color-surface);
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
}

.dark .react-flow__controls {
  background: var(--color-surface);
  border-color: var(--color-border);
}

.dark .react-flow__controls-button {
  background: var(--color-surface);
  border-color: var(--color-border);
  color: var(--color-text);
}

.dark .react-flow__controls-button:hover {
  background: var(--color-background);
}

.dark .react-flow__minimap {
  background: var(--color-surface);
  border-color: var(--color-border);
}

/* 思维导图节点样式 */
.mindmap-node {
  padding: 8px 12px;
  border-radius: 6px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.mindmap-node:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.mindmap-node.root {
  font-size: 18px;
  font-weight: bold;
  padding: 12px 20px;
}

.mindmap-node.branch {
  font-size: 16px;
  font-weight: 600;
}

.mindmap-node.leaf {
  font-size: 14px;
  font-weight: normal;
}

/* 看板组件样式 */
.kanban-board {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--color-background);
  padding: 16px;
}

.kanban-columns {
  display: flex;
  gap: 16px;
  overflow-x: auto;
  flex: 1;
  padding-bottom: 16px;
}

.kanban-column {
  flex: 0 0 300px;
  background: var(--color-surface);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid var(--color-border);
  max-height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
}

.kanban-column-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border);
}

.kanban-column-header h3 {
  margin: 0;
  color: var(--color-text);
}

.kanban-cards {
  flex: 1;
  overflow-y: auto;
  padding-right: 4px;
}

.kanban-cards::-webkit-scrollbar {
  width: 6px;
}

.kanban-cards::-webkit-scrollbar-track {
  background: var(--color-background);
  border-radius: 3px;
}

.kanban-cards::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 3px;
}

.kanban-cards::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-secondary);
}

/* 看板卡片样式 */
.kanban-board .ant-card {
  border: 1px solid var(--color-border);
  background: var(--color-surface);
  transition: all 0.2s ease;
}

.kanban-board .ant-card:hover {
  border-color: var(--color-primary-500);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.kanban-board .ant-card-body {
  padding: 12px;
}

.kanban-board .ant-card-actions {
  background: var(--color-background);
  border-top: 1px solid var(--color-border);
}

.kanban-board .ant-card-actions > li {
  margin: 0;
}

.kanban-board .ant-card-actions > li > span {
  color: var(--color-text-secondary);
  transition: color 0.2s ease;
}

.kanban-board .ant-card-actions > li > span:hover {
  color: var(--color-primary-500);
}

/* 优先级标签样式 */
.kanban-board .ant-tag {
  border-radius: 4px;
  font-size: 11px;
  line-height: 16px;
  margin: 0 4px 4px 0;
}

/* 拖拽状态样式 */
.kanban-board .sortable-ghost {
  opacity: 0.5;
}

.kanban-board .sortable-chosen {
  transform: rotate(5deg);
}

.kanban-board .sortable-drag {
  transform: rotate(5deg);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 暗色主题下的看板样式 */
.dark .kanban-column {
  background: var(--color-surface);
  border-color: var(--color-border);
}

.dark .kanban-column-header {
  border-color: var(--color-border);
}

.dark .kanban-board .ant-card {
  background: var(--color-surface);
  border-color: var(--color-border);
}

.dark .kanban-board .ant-card:hover {
  border-color: var(--color-primary-500);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.25);
}

.dark .kanban-board .ant-card-actions {
  background: var(--color-background);
  border-color: var(--color-border);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .kanban-columns {
    flex-direction: column;
    gap: 12px;
  }

  .kanban-column {
    flex: none;
    max-height: 400px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    @apply w-full;
  }
  
  .layout-container {
    @apply px-2;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    @apply text-black bg-white;
  }
  
  .card {
    @apply shadow-none border border-gray-300;
  }
}
