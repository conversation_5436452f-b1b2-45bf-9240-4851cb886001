/**
 * 看板组件 - 基于 @dnd-kit 的现代实现
 * 支持卡片拖拽、状态管理、数据持久化等功能
 */

import React, { useState, useCallback, useMemo, useEffect } from 'react'
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCorners
} from '@dnd-kit/core'
import {
  SortableContext,
  useSortable,
  verticalListSortingStrategy
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { Card, Button, Input, Tag, Space, Modal, Form, Select, message, Tooltip } from 'antd'
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined,
  FlagOutlined
} from '@ant-design/icons'
import { BaseDocument } from '@types/index'

const { TextArea } = Input
const { Option } = Select

/**
 * 看板卡片优先级
 */
export enum CardPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

/**
 * 看板卡片状态
 */
export enum CardStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  REVIEW = 'review',
  DONE = 'done'
}

/**
 * 看板卡片接口
 */
export interface KanbanCard {
  id: string
  title: string
  description: string
  status: CardStatus
  priority: CardPriority
  tags: string[]
  createdAt: Date
  updatedAt: Date
}

/**
 * 看板列接口
 */
export interface KanbanColumn {
  id: string
  title: string
  status: CardStatus
  color?: string
}

/**
 * 看板数据接口
 */
export interface KanbanData {
  columns: KanbanColumn[]
  cards: KanbanCard[]
}

/**
 * 看板组件属性接口
 */
export interface KanbanBoardProps {
  document?: BaseDocument
  readonly?: boolean
  onChange?: (data: string) => void
  className?: string
}

/**
 * 优先级颜色映射
 */
const PRIORITY_COLORS = {
  [CardPriority.LOW]: '#52c41a',
  [CardPriority.MEDIUM]: '#1890ff',
  [CardPriority.HIGH]: '#fa8c16',
  [CardPriority.URGENT]: '#f5222d'
}

/**
 * 优先级标签映射
 */
const PRIORITY_LABELS = {
  [CardPriority.LOW]: '低',
  [CardPriority.MEDIUM]: '中',
  [CardPriority.HIGH]: '高',
  [CardPriority.URGENT]: '紧急'
}

/**
 * 默认看板数据
 */
const DEFAULT_KANBAN_DATA: KanbanData = {
  columns: [
    { id: 'todo', title: '待办', status: CardStatus.TODO, color: '#f0f0f0' },
    { id: 'in_progress', title: '进行中', status: CardStatus.IN_PROGRESS, color: '#e6f7ff' },
    { id: 'review', title: '待审核', status: CardStatus.REVIEW, color: '#fff7e6' },
    { id: 'done', title: '已完成', status: CardStatus.DONE, color: '#f6ffed' }
  ],
  cards: []
}

/**
 * 可拖拽卡片组件
 */
const DraggableCard: React.FC<{
  card: KanbanCard
  onEdit: (card: KanbanCard) => void
  onDelete: (cardId: string) => void
  readonly?: boolean
}> = ({ card, onEdit, onDelete, readonly }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: card.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1
  }

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      <Card
        size="small"
        className="mb-3 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
        actions={!readonly ? [
          <EditOutlined key="edit" onClick={(e) => { e.stopPropagation(); onEdit(card) }} />,
          <DeleteOutlined key="delete" onClick={(e) => { e.stopPropagation(); onDelete(card.id) }} />
        ] : undefined}
      >
        <div className="mb-2">
          <h4 className="text-sm font-medium mb-1">{card.title}</h4>
          {card.description && (
            <p className="text-xs text-gray-600">{card.description}</p>
          )}
        </div>
        
        <div className="flex flex-wrap gap-1 mb-2">
          <Tag color={PRIORITY_COLORS[card.priority]} size="small" icon={<FlagOutlined />}>
            {PRIORITY_LABELS[card.priority]}
          </Tag>
          {card.tags.map(tag => (
            <Tag key={tag} size="small">{tag}</Tag>
          ))}
        </div>
      </Card>
    </div>
  )
}

/**
 * 看板列组件
 */
const KanbanColumnComponent: React.FC<{
  column: KanbanColumn
  cards: KanbanCard[]
  onAddCard: (status: CardStatus) => void
  onEditCard: (card: KanbanCard) => void
  onDeleteCard: (cardId: string) => void
  readonly?: boolean
}> = ({ column, cards, onAddCard, onEditCard, onDeleteCard, readonly }) => {
  return (
    <div className="kanban-column" style={{ backgroundColor: column.color }}>
      <div className="kanban-column-header">
        <h3 className="text-lg font-semibold mb-4">{column.title}</h3>
        <span className="text-sm text-gray-500">({cards.length})</span>
        {!readonly && (
          <Button
            type="dashed"
            size="small"
            icon={<PlusOutlined />}
            onClick={() => onAddCard(column.status)}
            className="ml-auto"
          >
            添加卡片
          </Button>
        )}
      </div>
      
      <SortableContext items={cards.map(c => c.id)} strategy={verticalListSortingStrategy}>
        <div className="kanban-cards">
          {cards.map(card => (
            <DraggableCard
              key={card.id}
              card={card}
              onEdit={onEditCard}
              onDelete={onDeleteCard}
              readonly={readonly}
            />
          ))}
        </div>
      </SortableContext>
    </div>
  )
}

/**
 * 看板组件主体
 */
const KanbanBoard: React.FC<KanbanBoardProps> = ({
  document,
  readonly = false,
  onChange,
  className = ''
}) => {
  const [kanbanData, setKanbanData] = useState<KanbanData>(DEFAULT_KANBAN_DATA)
  const [activeCard, setActiveCard] = useState<KanbanCard | null>(null)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [editingCard, setEditingCard] = useState<KanbanCard | null>(null)
  const [selectedStatus, setSelectedStatus] = useState<CardStatus | null>(null)
  const [form] = Form.useForm()

  // 配置拖拽传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: { distance: 8 }
    })
  )

  /**
   * 生成唯一ID
   */
  const generateId = useCallback(() => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }, [])

  /**
   * 处理拖拽开始
   */
  const handleDragStart = useCallback((event: DragStartEvent) => {
    const card = kanbanData.cards.find(c => c.id === event.active.id)
    setActiveCard(card || null)
  }, [kanbanData.cards])

  /**
   * 处理拖拽结束
   */
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event
    
    setActiveCard(null)
    
    if (!over || active.id === over.id) return

    const cardId = active.id as string
    const newStatus = over.id as CardStatus

    setKanbanData(prevData => {
      const updatedCards = prevData.cards.map(card =>
        card.id === cardId
          ? { ...card, status: newStatus, updatedAt: new Date() }
          : card
      )

      const newData = { ...prevData, cards: updatedCards }
      
      if (onChange) {
        onChange(JSON.stringify(newData))
      }

      return newData
    })
  }, [onChange])

  /**
   * 添加卡片
   */
  const handleAddCard = useCallback((status: CardStatus) => {
    setSelectedStatus(status)
    setEditingCard(null)
    form.resetFields()
    setIsModalVisible(true)
  }, [form])

  /**
   * 编辑卡片
   */
  const handleEditCard = useCallback((card: KanbanCard) => {
    setEditingCard(card)
    setSelectedStatus(null)
    form.setFieldsValue({
      title: card.title,
      description: card.description,
      priority: card.priority,
      tags: card.tags
    })
    setIsModalVisible(true)
  }, [form])

  /**
   * 删除卡片
   */
  const handleDeleteCard = useCallback((cardId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这张卡片吗？',
      onOk: () => {
        setKanbanData(prevData => {
          const newData = {
            ...prevData,
            cards: prevData.cards.filter(card => card.id !== cardId)
          }
          
          if (onChange) {
            onChange(JSON.stringify(newData))
          }
          
          return newData
        })
        message.success('卡片删除成功')
      }
    })
  }, [onChange])

  /**
   * 保存卡片
   */
  const handleSaveCard = useCallback(async () => {
    try {
      const values = await form.validateFields()
      const now = new Date()

      setKanbanData(prevData => {
        let newData: KanbanData

        if (editingCard) {
          // 更新现有卡片
          newData = {
            ...prevData,
            cards: prevData.cards.map(card =>
              card.id === editingCard.id
                ? { ...card, ...values, updatedAt: now }
                : card
            )
          }
          message.success('卡片更新成功')
        } else {
          // 创建新卡片
          const newCard: KanbanCard = {
            id: generateId(),
            ...values,
            status: selectedStatus || CardStatus.TODO,
            createdAt: now,
            updatedAt: now
          }
          
          newData = {
            ...prevData,
            cards: [...prevData.cards, newCard]
          }
          message.success('卡片创建成功')
        }

        if (onChange) {
          onChange(JSON.stringify(newData))
        }

        return newData
      })

      setIsModalVisible(false)
    } catch (error) {
      console.error('保存卡片失败:', error)
    }
  }, [editingCard, selectedStatus, form, generateId, onChange])

  /**
   * 加载文档内容
   */
  useEffect(() => {
    if (!document?.content) return

    try {
      const data = JSON.parse(document.content) as KanbanData
      if (data.columns && data.cards) {
        setKanbanData(data)
      }
    } catch (error) {
      console.error('加载看板内容失败:', error)
    }
  }, [document?.content])

  return (
    <div className={`kanban-board ${className}`}>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCorners}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <div className="kanban-columns">
          {kanbanData.columns.map(column => {
            const columnCards = kanbanData.cards.filter(card => card.status === column.status)
            return (
              <KanbanColumnComponent
                key={column.id}
                column={column}
                cards={columnCards}
                onAddCard={handleAddCard}
                onEditCard={handleEditCard}
                onDeleteCard={handleDeleteCard}
                readonly={readonly}
              />
            )
          })}
        </div>

        <DragOverlay>
          {activeCard && (
            <DraggableCard
              card={activeCard}
              onEdit={() => {}}
              onDelete={() => {}}
              readonly={true}
            />
          )}
        </DragOverlay>
      </DndContext>

      {/* 卡片编辑模态框 */}
      <Modal
        title={editingCard ? '编辑卡片' : '新建卡片'}
        open={isModalVisible}
        onOk={handleSaveCard}
        onCancel={() => setIsModalVisible(false)}
        okText="保存"
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="title"
            label="标题"
            rules={[{ required: true, message: '请输入卡片标题' }]}
          >
            <Input placeholder="请输入卡片标题" />
          </Form.Item>
          
          <Form.Item name="description" label="描述">
            <TextArea rows={3} placeholder="请输入卡片描述" />
          </Form.Item>
          
          <Form.Item name="priority" label="优先级" initialValue={CardPriority.MEDIUM}>
            <Select>
              {Object.entries(PRIORITY_LABELS).map(([value, label]) => (
                <Option key={value} value={value}>{label}</Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item name="tags" label="标签">
            <Select mode="tags" placeholder="请输入标签" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default KanbanBoard
