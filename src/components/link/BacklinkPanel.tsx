/**
 * 反向链接面板组件
 * 展示当前文档的所有入链和相关链接
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react'
import {
  Card,
  List,
  Button,
  Tag,
  Space,
  Tooltip,
  Empty,
  Spin,
  Typography,
  Divider,
  Avatar
} from 'antd'
import {
  LinkOutlined,
  FileTextOutlined,
  BgColorsOutlined,
  NodeIndexOutlined,
  ProjectOutlined,
  ClockCircleOutlined,
  DeleteOutlined,
  EyeOutlined,
  ArrowRightOutlined,
  ArrowLeftOutlined,
  EditOutlined,
  BranchesOutlined,
  TableOutlined
} from '@ant-design/icons'
import { BaseDocument, DocumentLink, DocumentType, LinkType } from '@types/index'
import { linkService } from '@services/link/linkService'
import { formatRelativeTime } from '@utils/index'

const { Text, Paragraph } = Typography

/**
 * 反向链接面板组件属性接口
 */
export interface BacklinkPanelProps {
  documentId: string
  onDocumentClick?: (document: BaseDocument) => void
  onLinkRemove?: (link: DocumentLink) => void
  className?: string
}

/**
 * 反向链接面板组件
 */
const BacklinkPanel: React.FC<BacklinkPanelProps> = ({
  documentId,
  onDocumentClick,
  onLinkRemove,
  className = ''
}) => {
  const [backlinks, setBacklinks] = useState<Array<{ document: BaseDocument; link: DocumentLink }>>([])
  const [loading, setLoading] = useState(true)
  const [removing, setRemoving] = useState<string | null>(null)

  /**
   * 加载反向链接
   */
  const loadBacklinks = useCallback(async () => {
    try {
      setLoading(true)
      const { incoming } = await linkService.getLinkedDocuments(documentId)
      setBacklinks(incoming)
    } catch (error) {
      console.error('加载反向链接失败:', error)
    } finally {
      setLoading(false)
    }
  }, [documentId])

  /**
   * 初始化加载
   */
  useEffect(() => {
    if (documentId) {
      loadBacklinks()
    }
  }, [documentId, loadBacklinks])

  /**
   * 处理文档点击
   */
  const handleDocumentClick = useCallback((document: BaseDocument) => {
    if (onDocumentClick) {
      onDocumentClick(document)
    }
  }, [onDocumentClick])

  /**
   * 处理链接删除
   */
  const handleLinkRemove = useCallback(async (link: DocumentLink) => {
    try {
      setRemoving(link.id)
      
      if (onLinkRemove) {
        onLinkRemove(link)
      }
      
      // 重新加载反向链接
      await loadBacklinks()
    } catch (error) {
      console.error('删除链接失败:', error)
    } finally {
      setRemoving(null)
    }
  }, [onLinkRemove, loadBacklinks])

  /**
   * 获取文档类型图标
   */
  const getDocumentIcon = (type: DocumentType) => {
    const icons = {
      [DocumentType.TEXT]: <FileTextOutlined className="text-blue-500" />,
      [DocumentType.WHITEBOARD]: <BgColorsOutlined className="text-green-500" />,
      [DocumentType.MINDMAP]: <NodeIndexOutlined className="text-purple-500" />,
      [DocumentType.KANBAN]: <ProjectOutlined className="text-orange-500" />
    }
    return icons[type]
  }

  /**
   * 获取链接类型标签
   */
  const getLinkTypeTag = (type: LinkType) => {
    const configs = {
      [LinkType.REFERENCE]: { color: 'blue', text: '引用' },
      [LinkType.EMBED]: { color: 'green', text: '嵌入' },
      [LinkType.RELATED]: { color: 'orange', text: '相关' },
      [LinkType.PARENT_CHILD]: { color: 'purple', text: '父子' }
    }
    
    const config = configs[type]
    return <Tag color={config.color} size="small">{config.text}</Tag>
  }

  /**
   * 获取文档类型名称
   */
  const getDocumentTypeName = (type: DocumentType) => {
    const names = {
      [DocumentType.TEXT]: '文本笔记',
      [DocumentType.WHITEBOARD]: '白板',
      [DocumentType.MINDMAP]: '思维导图',
      [DocumentType.KANBAN]: '看板'
    }
    return names[type]
  }

  if (loading) {
    return (
      <Card className={`backlink-panel ${className}`} title="反向链接">
        <div className="text-center py-8">
          <Spin tip="加载中..." />
        </div>
      </Card>
    )
  }

  return (
    <Card 
      className={`backlink-panel ${className}`}
      title={
        <Space>
          <LinkOutlined />
          <span>反向链接</span>
          <Text type="secondary">({backlinks.length})</Text>
        </Space>
      }
      size="small"
    >
      {backlinks.length === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="暂无反向链接"
          className="py-8"
        >
          <Text type="secondary" className="text-sm">
            当其他文档链接到此文档时，会在这里显示
          </Text>
        </Empty>
      ) : (
        <List
          dataSource={backlinks}
          renderItem={({ document, link }) => (
            <List.Item
              className="backlink-item hover:bg-gray-50 rounded p-2 cursor-pointer"
              onClick={() => handleDocumentClick(document)}
              actions={[
                <Tooltip key="remove" title="删除链接">
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    loading={removing === link.id}
                    onClick={(e) => {
                      e.stopPropagation()
                      handleLinkRemove(link)
                    }}
                    className="text-gray-400 hover:text-red-500"
                  />
                </Tooltip>
              ]}
            >
              <List.Item.Meta
                avatar={getDocumentIcon(document.type)}
                title={
                  <div className="flex items-center justify-between">
                    <Tooltip title={document.title}>
                      <Text className="text-sm font-medium truncate flex-1">
                        {document.title}
                      </Text>
                    </Tooltip>
                    {getLinkTypeTag(link.type)}
                  </div>
                }
                description={
                  <div className="space-y-1">
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{getDocumentTypeName(document.type)}</span>
                      <Space size="small">
                        <ClockCircleOutlined />
                        <span>{formatRelativeTime(document.updatedAt)}</span>
                      </Space>
                    </div>
                    
                    {link.label && (
                      <Text className="text-xs text-gray-600 italic">
                        {link.label}
                      </Text>
                    )}
                    
                    {document.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-1">
                        {document.tags.slice(0, 3).map(tag => (
                          <Tag key={tag} size="small" color="default">
                            {tag}
                          </Tag>
                        ))}
                        {document.tags.length > 3 && (
                          <Tag size="small" color="default">
                            +{document.tags.length - 3}
                          </Tag>
                        )}
                      </div>
                    )}
                  </div>
                }
              />
            </List.Item>
          )}
        />
      )}
      
      {backlinks.length > 0 && (
        <>
          <Divider className="my-3" />
          <div className="text-center">
            <Text type="secondary" className="text-xs">
              点击文档可以快速跳转，点击删除按钮可以移除链接
            </Text>
          </div>
        </>
      )}
    </Card>
  )
}

export default BacklinkPanel
