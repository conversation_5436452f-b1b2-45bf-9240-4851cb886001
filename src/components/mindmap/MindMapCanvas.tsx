/**
 * 思维导图画布组件
 * 基于 React Flow 实现的思维导图功能，支持节点创建、连接、布局等
 */

import React, { useCallback, useRef, useState, useEffect } from 'react'
import {
  ReactFlow,
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Node,
  Edge,
  Connection,
  BackgroundVariant,
  Panel,
  ReactFlowProvider,
  useReactFlow
} from '@xyflow/react'
import { Button, Space, Input, Select, ColorPicker, Tooltip, Divider } from 'antd'
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  SaveOutlined,
  DownloadOutlined,
  ExpandOutlined,
  CompressOutlined,
  BranchesOutlined,
  NodeIndexOutlined
} from '@ant-design/icons'
import { BaseDocument } from '@types/index'

// 导入 React Flow 样式
import '@xyflow/react/dist/style.css'

const { Option } = Select

/**
 * 思维导图节点类型
 */
export enum MindMapNodeType {
  ROOT = 'root',           // 根节点
  BRANCH = 'branch',       // 分支节点
  LEAF = 'leaf',          // 叶子节点
  NOTE = 'note'           // 注释节点
}

/**
 * 思维导图节点数据接口
 */
export interface MindMapNodeData {
  label: string
  description?: string
  color?: string
  fontSize?: number
  isEditing?: boolean
  level?: number
}

/**
 * 思维导图画布组件属性接口
 */
export interface MindMapCanvasProps {
  /** 文档数据 */
  document?: BaseDocument
  /** 画布宽度 */
  width?: number
  /** 画布高度 */
  height?: number
  /** 是否只读模式 */
  readonly?: boolean
  /** 内容变化回调 */
  onChange?: (data: string) => void
  /** 样式类名 */
  className?: string
}

/**
 * 初始节点数据
 */
const initialNodes: Node[] = [
  {
    id: 'root',
    type: MindMapNodeType.ROOT,
    position: { x: 400, y: 300 },
    data: {
      label: '中心主题',
      color: '#1890ff',
      fontSize: 18,
      level: 0
    },
    style: {
      background: '#1890ff',
      color: 'white',
      border: '2px solid #1890ff',
      borderRadius: '8px',
      padding: '10px 15px',
      fontSize: '18px',
      fontWeight: 'bold'
    }
  }
]

/**
 * 初始边数据
 */
const initialEdges: Edge[] = []

/**
 * 思维导图内部组件
 */
const MindMapFlow: React.FC<MindMapCanvasProps> = ({
  document,
  readonly = false,
  onChange,
  className = ''
}) => {
  const reactFlowInstance = useReactFlow()
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null)
  const [editingNodeId, setEditingNodeId] = useState<string | null>(null)
  const [newNodeLabel, setNewNodeLabel] = useState('')

  /**
   * 处理节点连接
   */
  const onConnect = useCallback(
    (params: Connection) => {
      const edge = {
        ...params,
        type: 'smoothstep',
        animated: true,
        style: { stroke: '#1890ff', strokeWidth: 2 }
      }
      setEdges((eds) => addEdge(edge, eds))
    },
    [setEdges]
  )

  /**
   * 处理节点选择
   */
  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    if (readonly) return
    setSelectedNodeId(node.id)
  }, [readonly])

  /**
   * 添加子节点
   */
  const addChildNode = useCallback(() => {
    if (!selectedNodeId || readonly) return

    const selectedNode = nodes.find(n => n.id === selectedNodeId)
    if (!selectedNode) return

    const newNodeId = `node-${Date.now()}`
    const parentLevel = (selectedNode.data.level || 0) + 1

    // 计算新节点位置
    const childrenCount = edges.filter(e => e.source === selectedNodeId).length
    const angle = (childrenCount * 60) - 30 // 分散角度
    const distance = 200
    const radian = (angle * Math.PI) / 180

    const newX = selectedNode.position.x + Math.cos(radian) * distance
    const newY = selectedNode.position.y + Math.sin(radian) * distance

    // 根据层级确定节点类型和样式
    const nodeType = parentLevel === 1 ? MindMapNodeType.BRANCH : MindMapNodeType.LEAF
    const nodeColor = parentLevel === 1 ? '#52c41a' : '#faad14'
    const fontSize = Math.max(14, 18 - parentLevel * 2)

    const newNode: Node = {
      id: newNodeId,
      type: nodeType,
      position: { x: newX, y: newY },
      data: {
        label: newNodeLabel || '新节点',
        color: nodeColor,
        fontSize,
        level: parentLevel
      },
      style: {
        background: nodeColor,
        color: 'white',
        border: `2px solid ${nodeColor}`,
        borderRadius: '6px',
        padding: '8px 12px',
        fontSize: `${fontSize}px`,
        fontWeight: parentLevel === 1 ? 'bold' : 'normal'
      }
    }

    const newEdge: Edge = {
      id: `edge-${selectedNodeId}-${newNodeId}`,
      source: selectedNodeId,
      target: newNodeId,
      type: 'smoothstep',
      animated: true,
      style: { stroke: nodeColor, strokeWidth: 2 }
    }

    setNodes((nds) => [...nds, newNode])
    setEdges((eds) => [...eds, newEdge])
    setNewNodeLabel('')
  }, [selectedNodeId, nodes, edges, newNodeLabel, readonly])

  /**
   * 删除选中节点
   */
  const deleteSelectedNode = useCallback(() => {
    if (!selectedNodeId || readonly || selectedNodeId === 'root') return

    // 递归删除子节点
    const deleteNodeAndChildren = (nodeId: string) => {
      const childEdges = edges.filter(e => e.source === nodeId)
      childEdges.forEach(edge => {
        deleteNodeAndChildren(edge.target)
      })

      setNodes((nds) => nds.filter(n => n.id !== nodeId))
      setEdges((eds) => eds.filter(e => e.source !== nodeId && e.target !== nodeId))
    }

    deleteNodeAndChildren(selectedNodeId)
    setSelectedNodeId(null)
  }, [selectedNodeId, nodes, edges, readonly])

  /**
   * 开始编辑节点
   */
  const startEditNode = useCallback(() => {
    if (!selectedNodeId || readonly) return
    setEditingNodeId(selectedNodeId)

    const node = nodes.find(n => n.id === selectedNodeId)
    if (node) {
      setNewNodeLabel(node.data.label)
    }
  }, [selectedNodeId, nodes, readonly])

  /**
   * 完成编辑节点
   */
  const finishEditNode = useCallback(() => {
    if (!editingNodeId || !newNodeLabel.trim()) return

    setNodes((nds) =>
      nds.map((node) =>
        node.id === editingNodeId
          ? {
              ...node,
              data: { ...node.data, label: newNodeLabel.trim() }
            }
          : node
      )
    )

    setEditingNodeId(null)
    setNewNodeLabel('')
  }, [editingNodeId, newNodeLabel])

  /**
   * 自动布局
   */
  const autoLayout = useCallback(() => {
    if (readonly) return

    // 简单的径向布局算法
    const rootNode = nodes.find(n => n.id === 'root')
    if (!rootNode) return

    const layoutNodes = [...nodes]
    const visited = new Set<string>()

    const layoutChildren = (parentId: string, parentPos: { x: number; y: number }, level: number) => {
      const childEdges = edges.filter(e => e.source === parentId && !visited.has(e.target))
      const childCount = childEdges.length

      if (childCount === 0) return

      const angleStep = (2 * Math.PI) / Math.max(childCount, 4)
      const radius = 150 + level * 50

      childEdges.forEach((edge, index) => {
        if (visited.has(edge.target)) return

        visited.add(edge.target)
        const angle = index * angleStep
        const x = parentPos.x + Math.cos(angle) * radius
        const y = parentPos.y + Math.sin(angle) * radius

        const nodeIndex = layoutNodes.findIndex(n => n.id === edge.target)
        if (nodeIndex !== -1) {
          layoutNodes[nodeIndex] = {
            ...layoutNodes[nodeIndex],
            position: { x, y }
          }

          layoutChildren(edge.target, { x, y }, level + 1)
        }
      })
    }

    visited.add('root')
    layoutChildren('root', rootNode.position, 1)
    setNodes(layoutNodes)
  }, [nodes, edges, readonly])

  /**
   * 适应视图
   */
  const fitView = useCallback(() => {
    reactFlowInstance.fitView({ padding: 50 })
  }, [reactFlowInstance])

  /**
   * 导出思维导图
   */
  const exportMindMap = useCallback(() => {
    const mindMapData = {
      nodes: nodes.map(node => ({
        id: node.id,
        type: node.type,
        position: node.position,
        data: node.data
      })),
      edges: edges.map(edge => ({
        id: edge.id,
        source: edge.source,
        target: edge.target,
        type: edge.type
      }))
    }

    const dataStr = JSON.stringify(mindMapData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)

    const link = document.createElement('a')
    link.href = url
    link.download = `mindmap-${Date.now()}.json`
    link.click()

    URL.revokeObjectURL(url)
  }, [nodes, edges])

  /**
   * 处理内容变化
   */
  useEffect(() => {
    if (readonly) return

    const mindMapData = JSON.stringify({ nodes, edges })
    onChange?.(mindMapData)
  }, [nodes, edges, onChange, readonly])

  /**
   * 加载文档内容
   */
  useEffect(() => {
    if (!document?.content) return

    try {
      const data = JSON.parse(document.content)
      if (data.nodes && data.edges) {
        setNodes(data.nodes)
        setEdges(data.edges)
      }
    } catch (error) {
      console.error('加载思维导图内容失败:', error)
    }
  }, [document?.content, setNodes, setEdges])

  return (
    <div className={`mindmap-canvas ${className}`} style={{ width: '100%', height: '100%' }}>
      {!readonly && (
        <Panel position="top-left">
          <div className="mindmap-toolbar">
            <Space wrap>
              <Input
                placeholder="节点标题"
                value={newNodeLabel}
                onChange={(e) => setNewNodeLabel(e.target.value)}
                onPressEnter={editingNodeId ? finishEditNode : addChildNode}
                style={{ width: 150 }}
              />

              {editingNodeId ? (
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  onClick={finishEditNode}
                  disabled={!newNodeLabel.trim()}
                >
                  保存
                </Button>
              ) : (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={addChildNode}
                  disabled={!selectedNodeId || !newNodeLabel.trim()}
                >
                  添加子节点
                </Button>
              )}

              <Divider type="vertical" />

              <Tooltip title="编辑节点">
                <Button
                  icon={<EditOutlined />}
                  onClick={startEditNode}
                  disabled={!selectedNodeId}
                />
              </Tooltip>

              <Tooltip title="删除节点">
                <Button
                  icon={<DeleteOutlined />}
                  onClick={deleteSelectedNode}
                  disabled={!selectedNodeId || selectedNodeId === 'root'}
                  danger
                />
              </Tooltip>

              <Divider type="vertical" />

              <Tooltip title="自动布局">
                <Button
                  icon={<BranchesOutlined />}
                  onClick={autoLayout}
                />
              </Tooltip>

              <Tooltip title="适应视图">
                <Button
                  icon={<CompressOutlined />}
                  onClick={fitView}
                />
              </Tooltip>

              <Tooltip title="导出">
                <Button
                  icon={<DownloadOutlined />}
                  onClick={exportMindMap}
                />
              </Tooltip>
            </Space>
          </div>
        </Panel>
      )}

      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeClick={onNodeClick}
        fitView
        attributionPosition="bottom-left"
      >
        <Controls />
        <MiniMap />
        <Background variant={BackgroundVariant.Dots} />
      </ReactFlow>
    </div>
  )
}

/**
 * 思维导图画布组件（带Provider包装）
 */
const MindMapCanvas: React.FC<MindMapCanvasProps> = (props) => {
  return (
    <ReactFlowProvider>
      <MindMapFlow {...props} />
    </ReactFlowProvider>
  )
}

export default MindMapCanvas
