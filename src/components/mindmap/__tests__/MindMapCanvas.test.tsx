/**
 * 思维导图组件单元测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { ReactFlowProvider } from '@xyflow/react'
import MindMapCanvas, { MindMapNodeType } from '../MindMapCanvas'
import { BaseDocument, DocumentType } from '@types/index'

// Mock React Flow
jest.mock('@xyflow/react', () => ({
  ...jest.requireActual('@xyflow/react'),
  ReactFlow: ({ children, ...props }: any) => (
    <div data-testid="react-flow" {...props}>
      {children}
    </div>
  ),
  Controls: () => <div data-testid="controls" />,
  MiniMap: () => <div data-testid="minimap" />,
  Background: () => <div data-testid="background" />,
  Panel: ({ children }: any) => <div data-testid="panel">{children}</div>,
  useReactFlow: () => ({
    fitView: jest.fn(),
    getNodes: jest.fn(() => []),
    getEdges: jest.fn(() => [])
  }),
  useNodesState: () => [[], jest.fn(), jest.fn()],
  useEdgesState: () => [[], jest.fn(), jest.fn()],
  addEdge: jest.fn()
}))

// 测试用的文档数据
const mockDocument: BaseDocument = {
  id: 'test-doc-1',
  title: '测试思维导图',
  content: JSON.stringify({
    nodes: [
      {
        id: 'root',
        type: MindMapNodeType.ROOT,
        position: { x: 400, y: 300 },
        data: { label: '中心主题', level: 0 }
      }
    ],
    edges: []
  }),
  type: DocumentType.MINDMAP,
  tags: ['测试'],
  createdAt: new Date(),
  updatedAt: new Date()
}

// 包装组件以提供必要的上下文
const MindMapWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ReactFlowProvider>
    {children}
  </ReactFlowProvider>
)

describe('MindMapCanvas', () => {
  beforeEach(() => {
    // 清除所有mock
    jest.clearAllMocks()
  })

  describe('基础渲染', () => {
    it('应该正确渲染思维导图组件', () => {
      render(
        <MindMapWrapper>
          <MindMapCanvas />
        </MindMapWrapper>
      )

      expect(screen.getByTestId('react-flow')).toBeInTheDocument()
      expect(screen.getByTestId('controls')).toBeInTheDocument()
      expect(screen.getByTestId('minimap')).toBeInTheDocument()
      expect(screen.getByTestId('background')).toBeInTheDocument()
    })

    it('应该在非只读模式下显示工具栏', () => {
      render(
        <MindMapWrapper>
          <MindMapCanvas readonly={false} />
        </MindMapWrapper>
      )

      expect(screen.getByTestId('panel')).toBeInTheDocument()
      expect(screen.getByPlaceholderText('节点标题')).toBeInTheDocument()
      expect(screen.getByText('添加子节点')).toBeInTheDocument()
    })

    it('应该在只读模式下隐藏工具栏', () => {
      render(
        <MindMapWrapper>
          <MindMapCanvas readonly={true} />
        </MindMapWrapper>
      )

      expect(screen.queryByPlaceholderText('节点标题')).not.toBeInTheDocument()
      expect(screen.queryByText('添加子节点')).not.toBeInTheDocument()
    })
  })

  describe('节点操作', () => {
    it('应该能够输入节点标题', () => {
      render(
        <MindMapWrapper>
          <MindMapCanvas />
        </MindMapWrapper>
      )

      const input = screen.getByPlaceholderText('节点标题')
      fireEvent.change(input, { target: { value: '新节点' } })

      expect(input).toHaveValue('新节点')
    })

    it('应该在没有选中节点时禁用添加按钮', () => {
      render(
        <MindMapWrapper>
          <MindMapCanvas />
        </MindMapWrapper>
      )

      const addButton = screen.getByText('添加子节点')
      expect(addButton).toBeDisabled()
    })

    it('应该在没有输入标题时禁用添加按钮', () => {
      render(
        <MindMapWrapper>
          <MindMapCanvas />
        </MindMapWrapper>
      )

      const addButton = screen.getByText('添加子节点')
      expect(addButton).toBeDisabled()
    })
  })

  describe('工具栏功能', () => {
    it('应该显示所有工具栏按钮', () => {
      render(
        <MindMapWrapper>
          <MindMapCanvas />
        </MindMapWrapper>
      )

      // 检查工具栏按钮
      expect(screen.getByText('添加子节点')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /编辑节点/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /删除节点/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /自动布局/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /适应视图/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /导出/i })).toBeInTheDocument()
    })

    it('应该在没有选中节点时禁用相关按钮', () => {
      render(
        <MindMapWrapper>
          <MindMapCanvas />
        </MindMapWrapper>
      )

      expect(screen.getByRole('button', { name: /编辑节点/i })).toBeDisabled()
      expect(screen.getByRole('button', { name: /删除节点/i })).toBeDisabled()
    })
  })

  describe('数据管理', () => {
    it('应该正确加载文档内容', () => {
      const onChange = jest.fn()
      
      render(
        <MindMapWrapper>
          <MindMapCanvas 
            document={mockDocument}
            onChange={onChange}
          />
        </MindMapWrapper>
      )

      // 验证组件正确渲染
      expect(screen.getByTestId('react-flow')).toBeInTheDocument()
    })

    it('应该在内容变化时调用onChange回调', async () => {
      const onChange = jest.fn()
      
      render(
        <MindMapWrapper>
          <MindMapCanvas onChange={onChange} />
        </MindMapWrapper>
      )

      // 由于我们mock了useNodesState和useEdgesState，
      // 这里主要测试组件是否正确设置了回调
      expect(onChange).toBeDefined()
    })
  })

  describe('键盘交互', () => {
    it('应该支持回车键添加节点', () => {
      render(
        <MindMapWrapper>
          <MindMapCanvas />
        </MindMapWrapper>
      )

      const input = screen.getByPlaceholderText('节点标题')
      fireEvent.change(input, { target: { value: '新节点' } })
      fireEvent.keyDown(input, { key: 'Enter', code: 'Enter' })

      // 由于我们mock了相关功能，这里主要测试事件绑定
      expect(input).toHaveValue('新节点')
    })
  })

  describe('样式和主题', () => {
    it('应该应用自定义类名', () => {
      render(
        <MindMapWrapper>
          <MindMapCanvas className="custom-mindmap" />
        </MindMapWrapper>
      )

      const container = screen.getByTestId('react-flow').parentElement
      expect(container).toHaveClass('custom-mindmap')
    })
  })

  describe('错误处理', () => {
    it('应该处理无效的文档内容', () => {
      const invalidDocument: BaseDocument = {
        ...mockDocument,
        content: 'invalid json'
      }

      // 应该不会抛出错误
      expect(() => {
        render(
          <MindMapWrapper>
            <MindMapCanvas document={invalidDocument} />
          </MindMapWrapper>
        )
      }).not.toThrow()
    })

    it('应该处理空文档内容', () => {
      const emptyDocument: BaseDocument = {
        ...mockDocument,
        content: ''
      }

      expect(() => {
        render(
          <MindMapWrapper>
            <MindMapCanvas document={emptyDocument} />
          </MindMapWrapper>
        )
      }).not.toThrow()
    })
  })

  describe('性能优化', () => {
    it('应该正确清理资源', () => {
      const { unmount } = render(
        <MindMapWrapper>
          <MindMapCanvas />
        </MindMapWrapper>
      )

      // 卸载组件应该不会抛出错误
      expect(() => unmount()).not.toThrow()
    })
  })
})
