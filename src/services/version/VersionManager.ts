/**
 * 版本管理系统
 * 实现文档的版本控制、历史记录和回滚功能
 */

import { BaseDocument } from '@types/index'

/**
 * 文档版本接口
 */
export interface DocumentVersion {
  id: string
  documentId: string
  version: number
  title: string
  content: string
  author: string
  comment?: string
  createdAt: Date
  parentVersion?: string
  tags: string[]
  metadata?: Record<string, any>
}

/**
 * 版本差异接口
 */
export interface VersionDiff {
  type: 'add' | 'remove' | 'modify'
  path: string
  oldValue?: any
  newValue?: any
  description: string
}

/**
 * 版本比较结果接口
 */
export interface VersionComparison {
  sourceVersion: DocumentVersion
  targetVersion: DocumentVersion
  differences: VersionDiff[]
  similarity: number
}

/**
 * 版本管理器类
 */
export class VersionManager {
  private versions: Map<string, DocumentVersion[]> = new Map()
  private maxVersionsPerDocument = 50

  /**
   * 创建新版本
   */
  createVersion(
    document: BaseDocument,
    author: string,
    comment?: string,
    parentVersion?: string
  ): DocumentVersion {
    const documentVersions = this.versions.get(document.id) || []
    const nextVersion = documentVersions.length + 1

    const version: DocumentVersion = {
      id: this.generateVersionId(),
      documentId: document.id,
      version: nextVersion,
      title: document.title,
      content: document.content,
      author,
      comment,
      createdAt: new Date(),
      parentVersion,
      tags: [...document.tags],
      metadata: {
        type: document.type,
        size: document.content.length,
        checksum: this.calculateChecksum(document.content)
      }
    }

    documentVersions.push(version)

    // 限制版本数量
    if (documentVersions.length > this.maxVersionsPerDocument) {
      documentVersions.shift() // 删除最旧的版本
    }

    this.versions.set(document.id, documentVersions)
    return version
  }

  /**
   * 获取文档的所有版本
   */
  getDocumentVersions(documentId: string): DocumentVersion[] {
    return this.versions.get(documentId) || []
  }

  /**
   * 获取特定版本
   */
  getVersion(documentId: string, versionNumber: number): DocumentVersion | null {
    const versions = this.getDocumentVersions(documentId)
    return versions.find(v => v.version === versionNumber) || null
  }

  /**
   * 获取最新版本
   */
  getLatestVersion(documentId: string): DocumentVersion | null {
    const versions = this.getDocumentVersions(documentId)
    return versions.length > 0 ? versions[versions.length - 1] : null
  }

  /**
   * 比较两个版本
   */
  compareVersions(
    documentId: string,
    sourceVersion: number,
    targetVersion: number
  ): VersionComparison | null {
    const source = this.getVersion(documentId, sourceVersion)
    const target = this.getVersion(documentId, targetVersion)

    if (!source || !target) return null

    const differences = this.calculateDifferences(source, target)
    const similarity = this.calculateSimilarity(source, target)

    return {
      sourceVersion: source,
      targetVersion: target,
      differences,
      similarity
    }
  }

  /**
   * 回滚到指定版本
   */
  revertToVersion(documentId: string, versionNumber: number): BaseDocument | null {
    const version = this.getVersion(documentId, versionNumber)
    if (!version) return null

    const revertedDocument: BaseDocument = {
      id: documentId,
      title: version.title,
      content: version.content,
      type: version.metadata?.type || 'text',
      tags: [...version.tags],
      createdAt: new Date(), // 保持原创建时间
      updatedAt: new Date(), // 更新修改时间
      author: version.author
    }

    return revertedDocument
  }

  /**
   * 删除版本
   */
  deleteVersion(documentId: string, versionNumber: number): boolean {
    const versions = this.getDocumentVersions(documentId)
    const index = versions.findIndex(v => v.version === versionNumber)
    
    if (index === -1) return false

    // 不允许删除最新版本
    if (versionNumber === versions[versions.length - 1].version) {
      return false
    }

    versions.splice(index, 1)
    this.versions.set(documentId, versions)
    return true
  }

  /**
   * 获取版本统计信息
   */
  getVersionStats(documentId: string): {
    totalVersions: number
    authors: string[]
    firstVersion: Date | null
    lastVersion: Date | null
    totalSize: number
  } {
    const versions = this.getDocumentVersions(documentId)
    
    if (versions.length === 0) {
      return {
        totalVersions: 0,
        authors: [],
        firstVersion: null,
        lastVersion: null,
        totalSize: 0
      }
    }

    const authors = [...new Set(versions.map(v => v.author))]
    const totalSize = versions.reduce((sum, v) => sum + v.content.length, 0)

    return {
      totalVersions: versions.length,
      authors,
      firstVersion: versions[0].createdAt,
      lastVersion: versions[versions.length - 1].createdAt,
      totalSize
    }
  }

  /**
   * 搜索版本
   */
  searchVersions(
    documentId: string,
    query: {
      author?: string
      dateRange?: { start: Date; end: Date }
      contentSearch?: string
      comment?: string
    }
  ): DocumentVersion[] {
    const versions = this.getDocumentVersions(documentId)
    
    return versions.filter(version => {
      // 作者筛选
      if (query.author && version.author !== query.author) {
        return false
      }

      // 日期范围筛选
      if (query.dateRange) {
        const versionDate = version.createdAt
        if (versionDate < query.dateRange.start || versionDate > query.dateRange.end) {
          return false
        }
      }

      // 内容搜索
      if (query.contentSearch) {
        const searchLower = query.contentSearch.toLowerCase()
        if (!version.content.toLowerCase().includes(searchLower) &&
            !version.title.toLowerCase().includes(searchLower)) {
          return false
        }
      }

      // 注释搜索
      if (query.comment && version.comment) {
        if (!version.comment.toLowerCase().includes(query.comment.toLowerCase())) {
          return false
        }
      }

      return true
    })
  }

  /**
   * 计算版本差异
   */
  private calculateDifferences(source: DocumentVersion, target: DocumentVersion): VersionDiff[] {
    const differences: VersionDiff[] = []

    // 标题变化
    if (source.title !== target.title) {
      differences.push({
        type: 'modify',
        path: 'title',
        oldValue: source.title,
        newValue: target.title,
        description: `标题从 "${source.title}" 改为 "${target.title}"`
      })
    }

    // 内容变化（简化的差异检测）
    if (source.content !== target.content) {
      const contentDiff = this.calculateContentDiff(source.content, target.content)
      differences.push({
        type: 'modify',
        path: 'content',
        oldValue: source.content.length,
        newValue: target.content.length,
        description: contentDiff
      })
    }

    // 标签变化
    const addedTags = target.tags.filter(tag => !source.tags.includes(tag))
    const removedTags = source.tags.filter(tag => !target.tags.includes(tag))

    addedTags.forEach(tag => {
      differences.push({
        type: 'add',
        path: 'tags',
        newValue: tag,
        description: `添加标签 "${tag}"`
      })
    })

    removedTags.forEach(tag => {
      differences.push({
        type: 'remove',
        path: 'tags',
        oldValue: tag,
        description: `删除标签 "${tag}"`
      })
    })

    return differences
  }

  /**
   * 计算内容差异描述
   */
  private calculateContentDiff(oldContent: string, newContent: string): string {
    const oldLength = oldContent.length
    const newLength = newContent.length
    const lengthDiff = newLength - oldLength

    if (lengthDiff > 0) {
      return `内容增加了 ${lengthDiff} 个字符`
    } else if (lengthDiff < 0) {
      return `内容减少了 ${Math.abs(lengthDiff)} 个字符`
    } else {
      return '内容长度未变，但内容有修改'
    }
  }

  /**
   * 计算版本相似度
   */
  private calculateSimilarity(source: DocumentVersion, target: DocumentVersion): number {
    // 简化的相似度计算
    const titleSimilarity = source.title === target.title ? 1 : 0
    const contentSimilarity = this.calculateContentSimilarity(source.content, target.content)
    const tagsSimilarity = this.calculateTagsSimilarity(source.tags, target.tags)

    return (titleSimilarity * 0.2 + contentSimilarity * 0.6 + tagsSimilarity * 0.2)
  }

  /**
   * 计算内容相似度
   */
  private calculateContentSimilarity(content1: string, content2: string): number {
    if (content1 === content2) return 1
    if (!content1 || !content2) return 0

    const maxLength = Math.max(content1.length, content2.length)
    const minLength = Math.min(content1.length, content2.length)
    
    return minLength / maxLength
  }

  /**
   * 计算标签相似度
   */
  private calculateTagsSimilarity(tags1: string[], tags2: string[]): number {
    if (tags1.length === 0 && tags2.length === 0) return 1
    
    const intersection = tags1.filter(tag => tags2.includes(tag))
    const union = [...new Set([...tags1, ...tags2])]
    
    return intersection.length / union.length
  }

  /**
   * 计算内容校验和
   */
  private calculateChecksum(content: string): string {
    let hash = 0
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return hash.toString(16)
  }

  /**
   * 生成版本ID
   */
  private generateVersionId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  /**
   * 清理文档的所有版本
   */
  clearDocumentVersions(documentId: string): void {
    this.versions.delete(documentId)
  }

  /**
   * 获取所有文档的版本统计
   */
  getAllVersionStats(): Map<string, any> {
    const stats = new Map()
    
    for (const [documentId] of this.versions) {
      stats.set(documentId, this.getVersionStats(documentId))
    }
    
    return stats
  }
}

// 导出单例实例
export const versionManager = new VersionManager()
