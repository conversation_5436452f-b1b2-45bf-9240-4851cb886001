{"name": "multidimensional-notes", "version": "0.1.0", "description": "现代化的多维度笔记应用，支持文本、白板、思维导图、看板等多种格式，并提供强大的关联系统", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@monaco-editor/react": "^4.6.0", "@tanstack/react-query": "^5.85.5", "@types/d3": "^7.4.3", "@types/react-beautiful-dnd": "^13.1.8", "@xyflow/react": "^12.8.4", "antd": "^5.12.8", "dayjs": "^1.11.10", "fabric": "^6.7.1", "idb": "^8.0.0", "katex": "^0.16.9", "marked": "^11.1.1", "monaco-editor": "^0.45.0", "prismjs": "^1.30.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "react-router-dom": "^6.20.1", "reactflow": "^11.11.4", "zustand": "^5.0.8"}, "devDependencies": {"@tailwindcss/typography": "^0.5.10", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/katex": "^0.16.7", "@types/prismjs": "^1.26.3", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jsdom": "^26.1.0", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8", "vite-plugin-pwa": "^1.0.3", "vitest": "^1.0.4"}, "keywords": ["notes", "markdown", "whiteboard", "mindmap", "kanban", "knowledge-management", "react", "typescript", "pwa"], "author": "Developer Team", "license": "MIT"}