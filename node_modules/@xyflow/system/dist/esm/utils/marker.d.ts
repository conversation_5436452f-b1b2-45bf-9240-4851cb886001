import type { EdgeBase, EdgeMarkerType, MarkerProps } from '../types';
export declare function getMarkerId(marker: EdgeMarkerType | undefined, id?: string | null): string;
export declare function createMarkerIds(edges: EdgeBase[], { id, defaultColor, defaultMarkerStart, defaultMarkerEnd, }: {
    id?: string | null;
    defaultColor?: string | null;
    defaultMarkerStart?: EdgeMarkerType;
    defaultMarkerEnd?: EdgeMarkerType;
}): MarkerProps[];
//# sourceMappingURL=marker.d.ts.map