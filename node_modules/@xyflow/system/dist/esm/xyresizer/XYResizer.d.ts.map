{"version": 3, "file": "XYResizer.d.ts", "sourceRoot": "", "sources": ["../../src/xyresizer/XYResizer.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,EAGV,QAAQ,EACR,UAAU,EACV,UAAU,EACV,SAAS,EACT,UAAU,EACX,MAAM,UAAU,CAAC;AAClB,OAAO,KAAK,EACV,QAAQ,EACR,WAAW,EACX,aAAa,EAEb,YAAY,EACZ,eAAe,EACf,sBAAsB,EACvB,MAAM,SAAS,CAAC;AAWjB,MAAM,MAAM,eAAe,GAAG;IAC5B,CAAC,CAAC,EAAE,MAAM,CAAC;IACX,CAAC,CAAC,EAAE,MAAM,CAAC;IACX,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB,CAAC;AAEF,MAAM,MAAM,oBAAoB,GAAG;IACjC,EAAE,EAAE,MAAM,CAAC;IACX,QAAQ,EAAE,UAAU,CAAC;IACrB,MAAM,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;CAC7B,CAAC;AAEF,KAAK,eAAe,GAAG;IACrB,OAAO,EAAE,cAAc,CAAC;IACxB,MAAM,EAAE,MAAM,CAAC;IACf,aAAa,EAAE,MAAM;QACnB,UAAU,EAAE,UAAU,CAAC;QACvB,SAAS,EAAE,SAAS,CAAC;QACrB,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC5B,UAAU,EAAE,OAAO,CAAC;QACpB,UAAU,EAAE,UAAU,CAAC;QACvB,WAAW,EAAE,cAAc,GAAG,IAAI,CAAC;KACpC,CAAC;IACF,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,YAAY,EAAE,oBAAoB,EAAE,KAAK,IAAI,CAAC;IACnF,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,eAAe,CAAC,KAAK,IAAI,CAAC;CACrD,CAAC;AAEF,KAAK,qBAAqB,GAAG;IAC3B,eAAe,EAAE,eAAe,CAAC;IACjC,UAAU,EAAE;QACV,QAAQ,EAAE,MAAM,CAAC;QACjB,SAAS,EAAE,MAAM,CAAC;QAClB,QAAQ,EAAE,MAAM,CAAC;QACjB,SAAS,EAAE,MAAM,CAAC;KACnB,CAAC;IACF,eAAe,EAAE,OAAO,CAAC;IACzB,eAAe,CAAC,EAAE,sBAAsB,CAAC;IACzC,aAAa,EAAE,aAAa,GAAG,SAAS,CAAC;IACzC,QAAQ,EAAE,QAAQ,GAAG,SAAS,CAAC;IAC/B,WAAW,EAAE,WAAW,GAAG,SAAS,CAAC;IACrC,YAAY,EAAE,YAAY,GAAG,SAAS,CAAC;CACxC,CAAC;AAEF,MAAM,MAAM,iBAAiB,GAAG;IAC9B,MAAM,EAAE,CAAC,MAAM,EAAE,qBAAqB,KAAK,IAAI,CAAC;IAChD,OAAO,EAAE,MAAM,IAAI,CAAC;CACrB,CAAC;AAuBF,wBAAgB,SAAS,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,eAAe,GAAG,iBAAiB,CAyNjH"}