declare const StoreContext: import("react").Context<import("zustand/traditional").UseBoundStoreWithEqualityFn<import("zustand").StoreApi<import("..").ReactFlowState>> | null>;
export declare const Provider: import("react").Provider<import("zustand/traditional").UseBoundStoreWithEqualityFn<import("zustand").StoreApi<import("..").ReactFlowState>> | null>;
export default StoreContext;
//# sourceMappingURL=StoreContext.d.ts.map