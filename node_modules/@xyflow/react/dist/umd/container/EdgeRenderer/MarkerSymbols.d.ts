import { MarkerType, type EdgeMarker } from '@xyflow/system';
type SymbolProps = Omit<EdgeMarker, 'type'>;
export declare const MarkerSymbols: {
    arrow: ({ color, strokeWidth }: SymbolProps) => import("react/jsx-runtime").JSX.Element;
    arrowclosed: ({ color, strokeWidth }: SymbolProps) => import("react/jsx-runtime").JSX.Element;
};
export declare function useMarkerSymbol(type: MarkerType | `${MarkerType}`): (({ color, strokeWidth }: SymbolProps) => import("react/jsx-runtime").JSX.Element) | null;
export {};
//# sourceMappingURL=MarkerSymbols.d.ts.map