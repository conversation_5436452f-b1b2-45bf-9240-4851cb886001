!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react/jsx-runtime"),require("react"),require("react-dom")):"function"==typeof define&&define.amd?define(["exports","react/jsx-runtime","react","react-dom"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactFlow={},e.jsxRuntime,e.React,e.ReactDOM)}(this,(function(e,t,n,o){"use strict";function r(e){if("string"==typeof e||"number"==typeof e)return""+e;let t="";if(Array.isArray(e))for(let n,o=0;o<e.length;o++)""!==(n=r(e[o]))&&(t+=(t&&" ")+n);else for(let n in e)e[n]&&(t+=(t&&" ")+n);return t}var i={value:()=>{}};function a(){for(var e,t=0,n=arguments.length,o={};t<n;++t){if(!(e=arguments[t]+"")||e in o||/[\s.]/.test(e))throw new Error("illegal type: "+e);o[e]=[]}return new s(o)}function s(e){this._=e}function l(e,t){for(var n,o=0,r=e.length;o<r;++o)if((n=e[o]).name===t)return n.value}function c(e,t,n){for(var o=0,r=e.length;o<r;++o)if(e[o].name===t){e[o]=i,e=e.slice(0,o).concat(e.slice(o+1));break}return null!=n&&e.push({name:t,value:n}),e}s.prototype=a.prototype={constructor:s,on:function(e,t){var n,o,r=this._,i=(o=r,(e+"").trim().split(/^|\s+/).map((function(e){var t="",n=e.indexOf(".");if(n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),e&&!o.hasOwnProperty(e))throw new Error("unknown type: "+e);return{type:e,name:t}}))),a=-1,s=i.length;if(!(arguments.length<2)){if(null!=t&&"function"!=typeof t)throw new Error("invalid callback: "+t);for(;++a<s;)if(n=(e=i[a]).type)r[n]=c(r[n],e.name,t);else if(null==t)for(n in r)r[n]=c(r[n],e.name,null);return this}for(;++a<s;)if((n=(e=i[a]).type)&&(n=l(r[n],e.name)))return n},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new s(e)},call:function(e,t){if((n=arguments.length-2)>0)for(var n,o,r=new Array(n),i=0;i<n;++i)r[i]=arguments[i+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(i=0,n=(o=this._[e]).length;i<n;++i)o[i].value.apply(t,r)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var o=this._[e],r=0,i=o.length;r<i;++r)o[r].value.apply(t,n)}};var u="http://www.w3.org/1999/xhtml",d={svg:"http://www.w3.org/2000/svg",xhtml:u,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function h(e){var t=e+="",n=t.indexOf(":");return n>=0&&"xmlns"!==(t=e.slice(0,n))&&(e=e.slice(n+1)),d.hasOwnProperty(t)?{space:d[t],local:e}:e}function f(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===u&&t.documentElement.namespaceURI===u?t.createElement(e):t.createElementNS(n,e)}}function g(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function p(e){var t=h(e);return(t.local?g:f)(t)}function m(){}function y(e){return null==e?m:function(){return this.querySelector(e)}}function v(){return[]}function x(e){return null==e?v:function(){return this.querySelectorAll(e)}}function w(e){return function(){return null==(t=e.apply(this,arguments))?[]:Array.isArray(t)?t:Array.from(t);var t}}function b(e){return function(){return this.matches(e)}}function S(e){return function(t){return t.matches(e)}}var C=Array.prototype.find;function E(){return this.firstElementChild}var k=Array.prototype.filter;function M(){return Array.from(this.children)}function N(e){return new Array(e.length)}function _(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}function P(e,t,n,o,r,i){for(var a,s=0,l=t.length,c=i.length;s<c;++s)(a=t[s])?(a.__data__=i[s],o[s]=a):n[s]=new _(e,i[s]);for(;s<l;++s)(a=t[s])&&(r[s]=a)}function z(e,t,n,o,r,i,a){var s,l,c,u=new Map,d=t.length,h=i.length,f=new Array(d);for(s=0;s<d;++s)(l=t[s])&&(f[s]=c=a.call(l,l.__data__,s,t)+"",u.has(c)?r[s]=l:u.set(c,l));for(s=0;s<h;++s)c=a.call(e,i[s],s,i)+"",(l=u.get(c))?(o[s]=l,l.__data__=i[s],u.delete(c)):n[s]=new _(e,i[s]);for(s=0;s<d;++s)(l=t[s])&&u.get(f[s])===l&&(r[s]=l)}function O(e){return e.__data__}function A(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function D(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function I(e){return function(){this.removeAttribute(e)}}function R(e){return function(){this.removeAttributeNS(e.space,e.local)}}function L(e,t){return function(){this.setAttribute(e,t)}}function $(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function T(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttribute(e):this.setAttribute(e,n)}}function V(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function B(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function j(e){return function(){this.style.removeProperty(e)}}function H(e,t,n){return function(){this.style.setProperty(e,t,n)}}function Z(e,t,n){return function(){var o=t.apply(this,arguments);null==o?this.style.removeProperty(e):this.style.setProperty(e,o,n)}}function X(e,t){return e.style.getPropertyValue(t)||B(e).getComputedStyle(e,null).getPropertyValue(t)}function Y(e){return function(){delete this[e]}}function F(e,t){return function(){this[e]=t}}function W(e,t){return function(){var n=t.apply(this,arguments);null==n?delete this[e]:this[e]=n}}function K(e){return e.trim().split(/^|\s+/)}function G(e){return e.classList||new q(e)}function q(e){this._node=e,this._names=K(e.getAttribute("class")||"")}function U(e,t){for(var n=G(e),o=-1,r=t.length;++o<r;)n.add(t[o])}function Q(e,t){for(var n=G(e),o=-1,r=t.length;++o<r;)n.remove(t[o])}function J(e){return function(){U(this,e)}}function ee(e){return function(){Q(this,e)}}function te(e,t){return function(){(t.apply(this,arguments)?U:Q)(this,e)}}function ne(){this.textContent=""}function oe(e){return function(){this.textContent=e}}function re(e){return function(){var t=e.apply(this,arguments);this.textContent=null==t?"":t}}function ie(){this.innerHTML=""}function ae(e){return function(){this.innerHTML=e}}function se(e){return function(){var t=e.apply(this,arguments);this.innerHTML=null==t?"":t}}function le(){this.nextSibling&&this.parentNode.appendChild(this)}function ce(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function ue(){return null}function de(){var e=this.parentNode;e&&e.removeChild(this)}function he(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function fe(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function ge(e){return function(){var t=this.__on;if(t){for(var n,o=0,r=-1,i=t.length;o<i;++o)n=t[o],e.type&&n.type!==e.type||n.name!==e.name?t[++r]=n:this.removeEventListener(n.type,n.listener,n.options);++r?t.length=r:delete this.__on}}}function pe(e,t,n){return function(){var o,r=this.__on,i=function(e){return function(t){e.call(this,t,this.__data__)}}(t);if(r)for(var a=0,s=r.length;a<s;++a)if((o=r[a]).type===e.type&&o.name===e.name)return this.removeEventListener(o.type,o.listener,o.options),this.addEventListener(o.type,o.listener=i,o.options=n),void(o.value=t);this.addEventListener(e.type,i,n),o={type:e.type,name:e.name,value:t,listener:i,options:n},r?r.push(o):this.__on=[o]}}function me(e,t,n){var o=B(e),r=o.CustomEvent;"function"==typeof r?r=new r(t,n):(r=o.document.createEvent("Event"),n?(r.initEvent(t,n.bubbles,n.cancelable),r.detail=n.detail):r.initEvent(t,!1,!1)),e.dispatchEvent(r)}function ye(e,t){return function(){return me(this,e,t)}}function ve(e,t){return function(){return me(this,e,t.apply(this,arguments))}}_.prototype={constructor:_,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}},q.prototype={add:function(e){this._names.indexOf(e)<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};var xe=[null];function we(e,t){this._groups=e,this._parents=t}function be(){return new we([[document.documentElement]],xe)}function Se(e){return"string"==typeof e?new we([[document.querySelector(e)]],[document.documentElement]):new we([[e]],xe)}function Ce(e,t){if(e=function(e){let t;for(;t=e.sourceEvent;)e=t;return e}(e),void 0===t&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var o=n.createSVGPoint();return o.x=e.clientX,o.y=e.clientY,[(o=o.matrixTransform(t.getScreenCTM().inverse())).x,o.y]}if(t.getBoundingClientRect){var r=t.getBoundingClientRect();return[e.clientX-r.left-t.clientLeft,e.clientY-r.top-t.clientTop]}}return[e.pageX,e.pageY]}we.prototype=be.prototype={constructor:we,select:function(e){"function"!=typeof e&&(e=y(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,a,s=t[r],l=s.length,c=o[r]=new Array(l),u=0;u<l;++u)(i=s[u])&&(a=e.call(i,i.__data__,u,s))&&("__data__"in i&&(a.__data__=i.__data__),c[u]=a);return new we(o,this._parents)},selectAll:function(e){e="function"==typeof e?w(e):x(e);for(var t=this._groups,n=t.length,o=[],r=[],i=0;i<n;++i)for(var a,s=t[i],l=s.length,c=0;c<l;++c)(a=s[c])&&(o.push(e.call(a,a.__data__,c,s)),r.push(a));return new we(o,r)},selectChild:function(e){return this.select(null==e?E:function(e){return function(){return C.call(this.children,e)}}("function"==typeof e?e:S(e)))},selectChildren:function(e){return this.selectAll(null==e?M:function(e){return function(){return k.call(this.children,e)}}("function"==typeof e?e:S(e)))},filter:function(e){"function"!=typeof e&&(e=b(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,a=t[r],s=a.length,l=o[r]=[],c=0;c<s;++c)(i=a[c])&&e.call(i,i.__data__,c,a)&&l.push(i);return new we(o,this._parents)},data:function(e,t){if(!arguments.length)return Array.from(this,O);var n,o=t?z:P,r=this._parents,i=this._groups;"function"!=typeof e&&(n=e,e=function(){return n});for(var a=i.length,s=new Array(a),l=new Array(a),c=new Array(a),u=0;u<a;++u){var d=r[u],h=i[u],f=h.length,g=A(e.call(d,d&&d.__data__,u,r)),p=g.length,m=l[u]=new Array(p),y=s[u]=new Array(p);o(d,h,m,y,c[u]=new Array(f),g,t);for(var v,x,w=0,b=0;w<p;++w)if(v=m[w]){for(w>=b&&(b=w+1);!(x=y[b])&&++b<p;);v._next=x||null}}return(s=new we(s,r))._enter=l,s._exit=c,s},enter:function(){return new we(this._enter||this._groups.map(N),this._parents)},exit:function(){return new we(this._exit||this._groups.map(N),this._parents)},join:function(e,t,n){var o=this.enter(),r=this,i=this.exit();return"function"==typeof e?(o=e(o))&&(o=o.selection()):o=o.append(e+""),null!=t&&(r=t(r))&&(r=r.selection()),null==n?i.remove():n(i),o&&r?o.merge(r).order():r},merge:function(e){for(var t=e.selection?e.selection():e,n=this._groups,o=t._groups,r=n.length,i=o.length,a=Math.min(r,i),s=new Array(r),l=0;l<a;++l)for(var c,u=n[l],d=o[l],h=u.length,f=s[l]=new Array(h),g=0;g<h;++g)(c=u[g]||d[g])&&(f[g]=c);for(;l<r;++l)s[l]=n[l];return new we(s,this._parents)},selection:function(){return this},order:function(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var o,r=e[t],i=r.length-1,a=r[i];--i>=0;)(o=r[i])&&(a&&4^o.compareDocumentPosition(a)&&a.parentNode.insertBefore(o,a),a=o);return this},sort:function(e){function t(t,n){return t&&n?e(t.__data__,n.__data__):!t-!n}e||(e=D);for(var n=this._groups,o=n.length,r=new Array(o),i=0;i<o;++i){for(var a,s=n[i],l=s.length,c=r[i]=new Array(l),u=0;u<l;++u)(a=s[u])&&(c[u]=a);c.sort(t)}return new we(r,this._parents).order()},call:function(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var o=e[t],r=0,i=o.length;r<i;++r){var a=o[r];if(a)return a}return null},size:function(){let e=0;for(const t of this)++e;return e},empty:function(){return!this.node()},each:function(e){for(var t=this._groups,n=0,o=t.length;n<o;++n)for(var r,i=t[n],a=0,s=i.length;a<s;++a)(r=i[a])&&e.call(r,r.__data__,a,i);return this},attr:function(e,t){var n=h(e);if(arguments.length<2){var o=this.node();return n.local?o.getAttributeNS(n.space,n.local):o.getAttribute(n)}return this.each((null==t?n.local?R:I:"function"==typeof t?n.local?V:T:n.local?$:L)(n,t))},style:function(e,t,n){return arguments.length>1?this.each((null==t?j:"function"==typeof t?Z:H)(e,t,null==n?"":n)):X(this.node(),e)},property:function(e,t){return arguments.length>1?this.each((null==t?Y:"function"==typeof t?W:F)(e,t)):this.node()[e]},classed:function(e,t){var n=K(e+"");if(arguments.length<2){for(var o=G(this.node()),r=-1,i=n.length;++r<i;)if(!o.contains(n[r]))return!1;return!0}return this.each(("function"==typeof t?te:t?J:ee)(n,t))},text:function(e){return arguments.length?this.each(null==e?ne:("function"==typeof e?re:oe)(e)):this.node().textContent},html:function(e){return arguments.length?this.each(null==e?ie:("function"==typeof e?se:ae)(e)):this.node().innerHTML},raise:function(){return this.each(le)},lower:function(){return this.each(ce)},append:function(e){var t="function"==typeof e?e:p(e);return this.select((function(){return this.appendChild(t.apply(this,arguments))}))},insert:function(e,t){var n="function"==typeof e?e:p(e),o=null==t?ue:"function"==typeof t?t:y(t);return this.select((function(){return this.insertBefore(n.apply(this,arguments),o.apply(this,arguments)||null)}))},remove:function(){return this.each(de)},clone:function(e){return this.select(e?fe:he)},datum:function(e){return arguments.length?this.property("__data__",e):this.node().__data__},on:function(e,t,n){var o,r,i=function(e){return e.trim().split(/^|\s+/).map((function(e){var t="",n=e.indexOf(".");return n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),{type:e,name:t}}))}(e+""),a=i.length;if(!(arguments.length<2)){for(s=t?pe:ge,o=0;o<a;++o)this.each(s(i[o],t,n));return this}var s=this.node().__on;if(s)for(var l,c=0,u=s.length;c<u;++c)for(o=0,l=s[c];o<a;++o)if((r=i[o]).type===l.type&&r.name===l.name)return l.value},dispatch:function(e,t){return this.each(("function"==typeof t?ve:ye)(e,t))},[Symbol.iterator]:function*(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var o,r=e[t],i=0,a=r.length;i<a;++i)(o=r[i])&&(yield o)}};const Ee={passive:!1},ke={capture:!0,passive:!1};function Me(e){e.stopImmediatePropagation()}function Ne(e){e.preventDefault(),e.stopImmediatePropagation()}function _e(e){var t=e.document.documentElement,n=Se(e).on("dragstart.drag",Ne,ke);"onselectstart"in t?n.on("selectstart.drag",Ne,ke):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function Pe(e,t){var n=e.document.documentElement,o=Se(e).on("dragstart.drag",null);t&&(o.on("click.drag",Ne,ke),setTimeout((function(){o.on("click.drag",null)}),0)),"onselectstart"in n?o.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}var ze=e=>()=>e;function Oe(e,{sourceEvent:t,subject:n,target:o,identifier:r,active:i,x:a,y:s,dx:l,dy:c,dispatch:u}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:o,enumerable:!0,configurable:!0},identifier:{value:r,enumerable:!0,configurable:!0},active:{value:i,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:s,enumerable:!0,configurable:!0},dx:{value:l,enumerable:!0,configurable:!0},dy:{value:c,enumerable:!0,configurable:!0},_:{value:u}})}function Ae(e){return!e.ctrlKey&&!e.button}function De(){return this.parentNode}function Ie(e,t){return null==t?{x:e.x,y:e.y}:t}function Re(){return navigator.maxTouchPoints||"ontouchstart"in this}function Le(){var e,t,n,o,r=Ae,i=De,s=Ie,l=Re,c={},u=a("start","drag","end"),d=0,h=0;function f(e){e.on("mousedown.drag",g).filter(l).on("touchstart.drag",y).on("touchmove.drag",v,Ee).on("touchend.drag touchcancel.drag",x).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function g(a,s){if(!o&&r.call(this,a,s)){var l=w(this,i.call(this,a,s),a,s,"mouse");l&&(Se(a.view).on("mousemove.drag",p,ke).on("mouseup.drag",m,ke),_e(a.view),Me(a),n=!1,e=a.clientX,t=a.clientY,l("start",a))}}function p(o){if(Ne(o),!n){var r=o.clientX-e,i=o.clientY-t;n=r*r+i*i>h}c.mouse("drag",o)}function m(e){Se(e.view).on("mousemove.drag mouseup.drag",null),Pe(e.view,n),Ne(e),c.mouse("end",e)}function y(e,t){if(r.call(this,e,t)){var n,o,a=e.changedTouches,s=i.call(this,e,t),l=a.length;for(n=0;n<l;++n)(o=w(this,s,e,t,a[n].identifier,a[n]))&&(Me(e),o("start",e,a[n]))}}function v(e){var t,n,o=e.changedTouches,r=o.length;for(t=0;t<r;++t)(n=c[o[t].identifier])&&(Ne(e),n("drag",e,o[t]))}function x(e){var t,n,r=e.changedTouches,i=r.length;for(o&&clearTimeout(o),o=setTimeout((function(){o=null}),500),t=0;t<i;++t)(n=c[r[t].identifier])&&(Me(e),n("end",e,r[t]))}function w(e,t,n,o,r,i){var a,l,h,g=u.copy(),p=Ce(i||n,t);if(null!=(h=s.call(e,new Oe("beforestart",{sourceEvent:n,target:f,identifier:r,active:d,x:p[0],y:p[1],dx:0,dy:0,dispatch:g}),o)))return a=h.x-p[0]||0,l=h.y-p[1]||0,function n(i,s,u){var m,y=p;switch(i){case"start":c[r]=n,m=d++;break;case"end":delete c[r],--d;case"drag":p=Ce(u||s,t),m=d}g.call(i,e,new Oe(i,{sourceEvent:s,subject:h,target:f,identifier:r,active:m,x:p[0]+a,y:p[1]+l,dx:p[0]-y[0],dy:p[1]-y[1],dispatch:g}),o)}}return f.filter=function(e){return arguments.length?(r="function"==typeof e?e:ze(!!e),f):r},f.container=function(e){return arguments.length?(i="function"==typeof e?e:ze(e),f):i},f.subject=function(e){return arguments.length?(s="function"==typeof e?e:ze(e),f):s},f.touchable=function(e){return arguments.length?(l="function"==typeof e?e:ze(!!e),f):l},f.on=function(){var e=u.on.apply(u,arguments);return e===u?f:e},f.clickDistance=function(e){return arguments.length?(h=(e=+e)*e,f):Math.sqrt(h)},f}function $e(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function Te(e,t){var n=Object.create(e.prototype);for(var o in t)n[o]=t[o];return n}function Ve(){}Oe.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e};var Be=.7,je=1/Be,He="\\s*([+-]?\\d+)\\s*",Ze="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Xe="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Ye=/^#([0-9a-f]{3,8})$/,Fe=new RegExp(`^rgb\\(${He},${He},${He}\\)$`),We=new RegExp(`^rgb\\(${Xe},${Xe},${Xe}\\)$`),Ke=new RegExp(`^rgba\\(${He},${He},${He},${Ze}\\)$`),Ge=new RegExp(`^rgba\\(${Xe},${Xe},${Xe},${Ze}\\)$`),qe=new RegExp(`^hsl\\(${Ze},${Xe},${Xe}\\)$`),Ue=new RegExp(`^hsla\\(${Ze},${Xe},${Xe},${Ze}\\)$`),Qe={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function Je(){return this.rgb().formatHex()}function et(){return this.rgb().formatRgb()}function tt(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=Ye.exec(e))?(n=t[1].length,t=parseInt(t[1],16),6===n?nt(t):3===n?new it(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===n?ot(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===n?ot(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=Fe.exec(e))?new it(t[1],t[2],t[3],1):(t=We.exec(e))?new it(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=Ke.exec(e))?ot(t[1],t[2],t[3],t[4]):(t=Ge.exec(e))?ot(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=qe.exec(e))?dt(t[1],t[2]/100,t[3]/100,1):(t=Ue.exec(e))?dt(t[1],t[2]/100,t[3]/100,t[4]):Qe.hasOwnProperty(e)?nt(Qe[e]):"transparent"===e?new it(NaN,NaN,NaN,0):null}function nt(e){return new it(e>>16&255,e>>8&255,255&e,1)}function ot(e,t,n,o){return o<=0&&(e=t=n=NaN),new it(e,t,n,o)}function rt(e,t,n,o){return 1===arguments.length?((r=e)instanceof Ve||(r=tt(r)),r?new it((r=r.rgb()).r,r.g,r.b,r.opacity):new it):new it(e,t,n,null==o?1:o);var r}function it(e,t,n,o){this.r=+e,this.g=+t,this.b=+n,this.opacity=+o}function at(){return`#${ut(this.r)}${ut(this.g)}${ut(this.b)}`}function st(){const e=lt(this.opacity);return`${1===e?"rgb(":"rgba("}${ct(this.r)}, ${ct(this.g)}, ${ct(this.b)}${1===e?")":`, ${e})`}`}function lt(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function ct(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function ut(e){return((e=ct(e))<16?"0":"")+e.toString(16)}function dt(e,t,n,o){return o<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new ft(e,t,n,o)}function ht(e){if(e instanceof ft)return new ft(e.h,e.s,e.l,e.opacity);if(e instanceof Ve||(e=tt(e)),!e)return new ft;if(e instanceof ft)return e;var t=(e=e.rgb()).r/255,n=e.g/255,o=e.b/255,r=Math.min(t,n,o),i=Math.max(t,n,o),a=NaN,s=i-r,l=(i+r)/2;return s?(a=t===i?(n-o)/s+6*(n<o):n===i?(o-t)/s+2:(t-n)/s+4,s/=l<.5?i+r:2-i-r,a*=60):s=l>0&&l<1?0:a,new ft(a,s,l,e.opacity)}function ft(e,t,n,o){this.h=+e,this.s=+t,this.l=+n,this.opacity=+o}function gt(e){return(e=(e||0)%360)<0?e+360:e}function pt(e){return Math.max(0,Math.min(1,e||0))}function mt(e,t,n){return 255*(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)}$e(Ve,tt,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Je,formatHex:Je,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return ht(this).formatHsl()},formatRgb:et,toString:et}),$e(it,rt,Te(Ve,{brighter(e){return e=null==e?je:Math.pow(je,e),new it(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?Be:Math.pow(Be,e),new it(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new it(ct(this.r),ct(this.g),ct(this.b),lt(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:at,formatHex:at,formatHex8:function(){return`#${ut(this.r)}${ut(this.g)}${ut(this.b)}${ut(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:st,toString:st})),$e(ft,(function(e,t,n,o){return 1===arguments.length?ht(e):new ft(e,t,n,null==o?1:o)}),Te(Ve,{brighter(e){return e=null==e?je:Math.pow(je,e),new ft(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?Be:Math.pow(Be,e),new ft(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+360*(this.h<0),t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,o=n+(n<.5?n:1-n)*t,r=2*n-o;return new it(mt(e>=240?e-240:e+120,r,o),mt(e,r,o),mt(e<120?e+240:e-120,r,o),this.opacity)},clamp(){return new ft(gt(this.h),pt(this.s),pt(this.l),lt(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=lt(this.opacity);return`${1===e?"hsl(":"hsla("}${gt(this.h)}, ${100*pt(this.s)}%, ${100*pt(this.l)}%${1===e?")":`, ${e})`}`}}));var yt=e=>()=>e;function vt(e){return 1==(e=+e)?xt:function(t,n){return n-t?function(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(o){return Math.pow(e+o*t,n)}}(t,n,e):yt(isNaN(t)?n:t)}}function xt(e,t){var n=t-e;return n?function(e,t){return function(n){return e+n*t}}(e,n):yt(isNaN(e)?t:e)}var wt=function e(t){var n=vt(t);function o(e,t){var o=n((e=rt(e)).r,(t=rt(t)).r),r=n(e.g,t.g),i=n(e.b,t.b),a=xt(e.opacity,t.opacity);return function(t){return e.r=o(t),e.g=r(t),e.b=i(t),e.opacity=a(t),e+""}}return o.gamma=e,o}(1);function bt(e,t){t||(t=[]);var n,o=e?Math.min(t.length,e.length):0,r=t.slice();return function(i){for(n=0;n<o;++n)r[n]=e[n]*(1-i)+t[n]*i;return r}}function St(e,t){var n,o=t?t.length:0,r=e?Math.min(o,e.length):0,i=new Array(r),a=new Array(o);for(n=0;n<r;++n)i[n]=Pt(e[n],t[n]);for(;n<o;++n)a[n]=t[n];return function(e){for(n=0;n<r;++n)a[n]=i[n](e);return a}}function Ct(e,t){var n=new Date;return e=+e,t=+t,function(o){return n.setTime(e*(1-o)+t*o),n}}function Et(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}function kt(e,t){var n,o={},r={};for(n in null!==e&&"object"==typeof e||(e={}),null!==t&&"object"==typeof t||(t={}),t)n in e?o[n]=Pt(e[n],t[n]):r[n]=t[n];return function(e){for(n in o)r[n]=o[n](e);return r}}var Mt=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Nt=new RegExp(Mt.source,"g");function _t(e,t){var n,o,r,i=Mt.lastIndex=Nt.lastIndex=0,a=-1,s=[],l=[];for(e+="",t+="";(n=Mt.exec(e))&&(o=Nt.exec(t));)(r=o.index)>i&&(r=t.slice(i,r),s[a]?s[a]+=r:s[++a]=r),(n=n[0])===(o=o[0])?s[a]?s[a]+=o:s[++a]=o:(s[++a]=null,l.push({i:a,x:Et(n,o)})),i=Nt.lastIndex;return i<t.length&&(r=t.slice(i),s[a]?s[a]+=r:s[++a]=r),s.length<2?l[0]?function(e){return function(t){return e(t)+""}}(l[0].x):function(e){return function(){return e}}(t):(t=l.length,function(e){for(var n,o=0;o<t;++o)s[(n=l[o]).i]=n.x(e);return s.join("")})}function Pt(e,t){var n,o,r=typeof t;return null==t||"boolean"===r?yt(t):("number"===r?Et:"string"===r?(n=tt(t))?(t=n,wt):_t:t instanceof tt?wt:t instanceof Date?Ct:(o=t,!ArrayBuffer.isView(o)||o instanceof DataView?Array.isArray(t)?St:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?kt:Et:bt))(e,t)}var zt,Ot=180/Math.PI,At={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Dt(e,t,n,o,r,i){var a,s,l;return(a=Math.sqrt(e*e+t*t))&&(e/=a,t/=a),(l=e*n+t*o)&&(n-=e*l,o-=t*l),(s=Math.sqrt(n*n+o*o))&&(n/=s,o/=s,l/=s),e*o<t*n&&(e=-e,t=-t,l=-l,a=-a),{translateX:r,translateY:i,rotate:Math.atan2(t,e)*Ot,skewX:Math.atan(l)*Ot,scaleX:a,scaleY:s}}function It(e,t,n,o){function r(e){return e.length?e.pop()+" ":""}return function(i,a){var s=[],l=[];return i=e(i),a=e(a),function(e,o,r,i,a,s){if(e!==r||o!==i){var l=a.push("translate(",null,t,null,n);s.push({i:l-4,x:Et(e,r)},{i:l-2,x:Et(o,i)})}else(r||i)&&a.push("translate("+r+t+i+n)}(i.translateX,i.translateY,a.translateX,a.translateY,s,l),function(e,t,n,i){e!==t?(e-t>180?t+=360:t-e>180&&(e+=360),i.push({i:n.push(r(n)+"rotate(",null,o)-2,x:Et(e,t)})):t&&n.push(r(n)+"rotate("+t+o)}(i.rotate,a.rotate,s,l),function(e,t,n,i){e!==t?i.push({i:n.push(r(n)+"skewX(",null,o)-2,x:Et(e,t)}):t&&n.push(r(n)+"skewX("+t+o)}(i.skewX,a.skewX,s,l),function(e,t,n,o,i,a){if(e!==n||t!==o){var s=i.push(r(i)+"scale(",null,",",null,")");a.push({i:s-4,x:Et(e,n)},{i:s-2,x:Et(t,o)})}else 1===n&&1===o||i.push(r(i)+"scale("+n+","+o+")")}(i.scaleX,i.scaleY,a.scaleX,a.scaleY,s,l),i=a=null,function(e){for(var t,n=-1,o=l.length;++n<o;)s[(t=l[n]).i]=t.x(e);return s.join("")}}}var Rt=It((function(e){const t=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?At:Dt(t.a,t.b,t.c,t.d,t.e,t.f)}),"px, ","px)","deg)"),Lt=It((function(e){return null==e?At:(zt||(zt=document.createElementNS("http://www.w3.org/2000/svg","g")),zt.setAttribute("transform",e),(e=zt.transform.baseVal.consolidate())?Dt((e=e.matrix).a,e.b,e.c,e.d,e.e,e.f):At)}),", ",")",")");function $t(e){return((e=Math.exp(e))+1/e)/2}var Tt,Vt,Bt=function e(t,n,o){function r(e,r){var i,a,s=e[0],l=e[1],c=e[2],u=r[0],d=r[1],h=r[2],f=u-s,g=d-l,p=f*f+g*g;if(p<1e-12)a=Math.log(h/c)/t,i=function(e){return[s+e*f,l+e*g,c*Math.exp(t*e*a)]};else{var m=Math.sqrt(p),y=(h*h-c*c+o*p)/(2*c*n*m),v=(h*h-c*c-o*p)/(2*h*n*m),x=Math.log(Math.sqrt(y*y+1)-y),w=Math.log(Math.sqrt(v*v+1)-v);a=(w-x)/t,i=function(e){var o,r=e*a,i=$t(x),u=c/(n*m)*(i*(o=t*r+x,((o=Math.exp(2*o))-1)/(o+1))-function(e){return((e=Math.exp(e))-1/e)/2}(x));return[s+u*f,l+u*g,c*i/$t(t*r+x)]}}return i.duration=1e3*a*t/Math.SQRT2,i}return r.rho=function(t){var n=Math.max(.001,+t),o=n*n;return e(n,o,o*o)},r}(Math.SQRT2,2,4),jt=0,Ht=0,Zt=0,Xt=1e3,Yt=0,Ft=0,Wt=0,Kt="object"==typeof performance&&performance.now?performance:Date,Gt="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function qt(){return Ft||(Gt(Ut),Ft=Kt.now()+Wt)}function Ut(){Ft=0}function Qt(){this._call=this._time=this._next=null}function Jt(e,t,n){var o=new Qt;return o.restart(e,t,n),o}function en(){Ft=(Yt=Kt.now())+Wt,jt=Ht=0;try{!function(){qt(),++jt;for(var e,t=Tt;t;)(e=Ft-t._time)>=0&&t._call.call(void 0,e),t=t._next;--jt}()}finally{jt=0,function(){var e,t,n=Tt,o=1/0;for(;n;)n._call?(o>n._time&&(o=n._time),e=n,n=n._next):(t=n._next,n._next=null,n=e?e._next=t:Tt=t);Vt=e,nn(o)}(),Ft=0}}function tn(){var e=Kt.now(),t=e-Yt;t>Xt&&(Wt-=t,Yt=e)}function nn(e){jt||(Ht&&(Ht=clearTimeout(Ht)),e-Ft>24?(e<1/0&&(Ht=setTimeout(en,e-Kt.now()-Wt)),Zt&&(Zt=clearInterval(Zt))):(Zt||(Yt=Kt.now(),Zt=setInterval(tn,Xt)),jt=1,Gt(en)))}function on(e,t,n){var o=new Qt;return t=null==t?0:+t,o.restart((n=>{o.stop(),e(n+t)}),t,n),o}Qt.prototype=Jt.prototype={constructor:Qt,restart:function(e,t,n){if("function"!=typeof e)throw new TypeError("callback is not a function");n=(null==n?qt():+n)+(null==t?0:+t),this._next||Vt===this||(Vt?Vt._next=this:Tt=this,Vt=this),this._call=e,this._time=n,nn()},stop:function(){this._call&&(this._call=null,this._time=1/0,nn())}};var rn=a("start","end","cancel","interrupt"),an=[],sn=0,ln=1,cn=2,un=3,dn=4,hn=5,fn=6;function gn(e,t,n,o,r,i){var a=e.__transition;if(a){if(n in a)return}else e.__transition={};!function(e,t,n){var o,r=e.__transition;function i(e){n.state=ln,n.timer.restart(a,n.delay,n.time),n.delay<=e&&a(e-n.delay)}function a(i){var c,u,d,h;if(n.state!==ln)return l();for(c in r)if((h=r[c]).name===n.name){if(h.state===un)return on(a);h.state===dn?(h.state=fn,h.timer.stop(),h.on.call("interrupt",e,e.__data__,h.index,h.group),delete r[c]):+c<t&&(h.state=fn,h.timer.stop(),h.on.call("cancel",e,e.__data__,h.index,h.group),delete r[c])}if(on((function(){n.state===un&&(n.state=dn,n.timer.restart(s,n.delay,n.time),s(i))})),n.state=cn,n.on.call("start",e,e.__data__,n.index,n.group),n.state===cn){for(n.state=un,o=new Array(d=n.tween.length),c=0,u=-1;c<d;++c)(h=n.tween[c].value.call(e,e.__data__,n.index,n.group))&&(o[++u]=h);o.length=u+1}}function s(t){for(var r=t<n.duration?n.ease.call(null,t/n.duration):(n.timer.restart(l),n.state=hn,1),i=-1,a=o.length;++i<a;)o[i].call(e,r);n.state===hn&&(n.on.call("end",e,e.__data__,n.index,n.group),l())}function l(){for(var o in n.state=fn,n.timer.stop(),delete r[t],r)return;delete e.__transition}r[t]=n,n.timer=Jt(i,0,n.time)}(e,n,{name:t,index:o,group:r,on:rn,tween:an,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:sn})}function pn(e,t){var n=yn(e,t);if(n.state>sn)throw new Error("too late; already scheduled");return n}function mn(e,t){var n=yn(e,t);if(n.state>un)throw new Error("too late; already running");return n}function yn(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function vn(e,t){var n,o,r,i=e.__transition,a=!0;if(i){for(r in t=null==t?null:t+"",i)(n=i[r]).name===t?(o=n.state>cn&&n.state<hn,n.state=fn,n.timer.stop(),n.on.call(o?"interrupt":"cancel",e,e.__data__,n.index,n.group),delete i[r]):a=!1;a&&delete e.__transition}}function xn(e,t){var n,o;return function(){var r=mn(this,e),i=r.tween;if(i!==n)for(var a=0,s=(o=n=i).length;a<s;++a)if(o[a].name===t){(o=o.slice()).splice(a,1);break}r.tween=o}}function wn(e,t,n){var o,r;if("function"!=typeof n)throw new Error;return function(){var i=mn(this,e),a=i.tween;if(a!==o){r=(o=a).slice();for(var s={name:t,value:n},l=0,c=r.length;l<c;++l)if(r[l].name===t){r[l]=s;break}l===c&&r.push(s)}i.tween=r}}function bn(e,t,n){var o=e._id;return e.each((function(){var e=mn(this,o);(e.value||(e.value={}))[t]=n.apply(this,arguments)})),function(e){return yn(e,o).value[t]}}function Sn(e,t){var n;return("number"==typeof t?Et:t instanceof tt?wt:(n=tt(t))?(t=n,wt):_t)(e,t)}function Cn(e){return function(){this.removeAttribute(e)}}function En(e){return function(){this.removeAttributeNS(e.space,e.local)}}function kn(e,t,n){var o,r,i=n+"";return function(){var a=this.getAttribute(e);return a===i?null:a===o?r:r=t(o=a,n)}}function Mn(e,t,n){var o,r,i=n+"";return function(){var a=this.getAttributeNS(e.space,e.local);return a===i?null:a===o?r:r=t(o=a,n)}}function Nn(e,t,n){var o,r,i;return function(){var a,s,l=n(this);if(null!=l)return(a=this.getAttribute(e))===(s=l+"")?null:a===o&&s===r?i:(r=s,i=t(o=a,l));this.removeAttribute(e)}}function _n(e,t,n){var o,r,i;return function(){var a,s,l=n(this);if(null!=l)return(a=this.getAttributeNS(e.space,e.local))===(s=l+"")?null:a===o&&s===r?i:(r=s,i=t(o=a,l));this.removeAttributeNS(e.space,e.local)}}function Pn(e,t){var n,o;function r(){var r=t.apply(this,arguments);return r!==o&&(n=(o=r)&&function(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}(e,r)),n}return r._value=t,r}function zn(e,t){var n,o;function r(){var r=t.apply(this,arguments);return r!==o&&(n=(o=r)&&function(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}(e,r)),n}return r._value=t,r}function On(e,t){return function(){pn(this,e).delay=+t.apply(this,arguments)}}function An(e,t){return t=+t,function(){pn(this,e).delay=t}}function Dn(e,t){return function(){mn(this,e).duration=+t.apply(this,arguments)}}function In(e,t){return t=+t,function(){mn(this,e).duration=t}}var Rn=be.prototype.constructor;function Ln(e){return function(){this.style.removeProperty(e)}}var $n=0;function Tn(e,t,n,o){this._groups=e,this._parents=t,this._name=n,this._id=o}function Vn(){return++$n}var Bn=be.prototype;Tn.prototype={constructor:Tn,select:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=y(e));for(var o=this._groups,r=o.length,i=new Array(r),a=0;a<r;++a)for(var s,l,c=o[a],u=c.length,d=i[a]=new Array(u),h=0;h<u;++h)(s=c[h])&&(l=e.call(s,s.__data__,h,c))&&("__data__"in s&&(l.__data__=s.__data__),d[h]=l,gn(d[h],t,n,h,d,yn(s,n)));return new Tn(i,this._parents,t,n)},selectAll:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=x(e));for(var o=this._groups,r=o.length,i=[],a=[],s=0;s<r;++s)for(var l,c=o[s],u=c.length,d=0;d<u;++d)if(l=c[d]){for(var h,f=e.call(l,l.__data__,d,c),g=yn(l,n),p=0,m=f.length;p<m;++p)(h=f[p])&&gn(h,t,n,p,f,g);i.push(f),a.push(l)}return new Tn(i,a,t,n)},selectChild:Bn.selectChild,selectChildren:Bn.selectChildren,filter:function(e){"function"!=typeof e&&(e=b(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,a=t[r],s=a.length,l=o[r]=[],c=0;c<s;++c)(i=a[c])&&e.call(i,i.__data__,c,a)&&l.push(i);return new Tn(o,this._parents,this._name,this._id)},merge:function(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,o=t.length,r=n.length,i=Math.min(o,r),a=new Array(o),s=0;s<i;++s)for(var l,c=t[s],u=n[s],d=c.length,h=a[s]=new Array(d),f=0;f<d;++f)(l=c[f]||u[f])&&(h[f]=l);for(;s<o;++s)a[s]=t[s];return new Tn(a,this._parents,this._name,this._id)},selection:function(){return new Rn(this._groups,this._parents)},transition:function(){for(var e=this._name,t=this._id,n=Vn(),o=this._groups,r=o.length,i=0;i<r;++i)for(var a,s=o[i],l=s.length,c=0;c<l;++c)if(a=s[c]){var u=yn(a,t);gn(a,e,n,c,s,{time:u.time+u.delay+u.duration,delay:0,duration:u.duration,ease:u.ease})}return new Tn(o,this._parents,e,n)},call:Bn.call,nodes:Bn.nodes,node:Bn.node,size:Bn.size,empty:Bn.empty,each:Bn.each,on:function(e,t){var n=this._id;return arguments.length<2?yn(this.node(),n).on.on(e):this.each(function(e,t,n){var o,r,i=function(e){return(e+"").trim().split(/^|\s+/).every((function(e){var t=e.indexOf(".");return t>=0&&(e=e.slice(0,t)),!e||"start"===e}))}(t)?pn:mn;return function(){var a=i(this,e),s=a.on;s!==o&&(r=(o=s).copy()).on(t,n),a.on=r}}(n,e,t))},attr:function(e,t){var n=h(e),o="transform"===n?Lt:Sn;return this.attrTween(e,"function"==typeof t?(n.local?_n:Nn)(n,o,bn(this,"attr."+e,t)):null==t?(n.local?En:Cn)(n):(n.local?Mn:kn)(n,o,t))},attrTween:function(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw new Error;var o=h(e);return this.tween(n,(o.local?Pn:zn)(o,t))},style:function(e,t,n){var o="transform"==(e+="")?Rt:Sn;return null==t?this.styleTween(e,function(e,t){var n,o,r;return function(){var i=X(this,e),a=(this.style.removeProperty(e),X(this,e));return i===a?null:i===n&&a===o?r:r=t(n=i,o=a)}}(e,o)).on("end.style."+e,Ln(e)):"function"==typeof t?this.styleTween(e,function(e,t,n){var o,r,i;return function(){var a=X(this,e),s=n(this),l=s+"";return null==s&&(this.style.removeProperty(e),l=s=X(this,e)),a===l?null:a===o&&l===r?i:(r=l,i=t(o=a,s))}}(e,o,bn(this,"style."+e,t))).each(function(e,t){var n,o,r,i,a="style."+t,s="end."+a;return function(){var l=mn(this,e),c=l.on,u=null==l.value[a]?i||(i=Ln(t)):void 0;c===n&&r===u||(o=(n=c).copy()).on(s,r=u),l.on=o}}(this._id,e)):this.styleTween(e,function(e,t,n){var o,r,i=n+"";return function(){var a=X(this,e);return a===i?null:a===o?r:r=t(o=a,n)}}(e,o,t),n).on("end.style."+e,null)},styleTween:function(e,t,n){var o="style."+(e+="");if(arguments.length<2)return(o=this.tween(o))&&o._value;if(null==t)return this.tween(o,null);if("function"!=typeof t)throw new Error;return this.tween(o,function(e,t,n){var o,r;function i(){var i=t.apply(this,arguments);return i!==r&&(o=(r=i)&&function(e,t,n){return function(o){this.style.setProperty(e,t.call(this,o),n)}}(e,i,n)),o}return i._value=t,i}(e,t,null==n?"":n))},text:function(e){return this.tween("text","function"==typeof e?function(e){return function(){var t=e(this);this.textContent=null==t?"":t}}(bn(this,"text",e)):function(e){return function(){this.textContent=e}}(null==e?"":e+""))},textTween:function(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(null==e)return this.tween(t,null);if("function"!=typeof e)throw new Error;return this.tween(t,function(e){var t,n;function o(){var o=e.apply(this,arguments);return o!==n&&(t=(n=o)&&function(e){return function(t){this.textContent=e.call(this,t)}}(o)),t}return o._value=e,o}(e))},remove:function(){return this.on("end.remove",function(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}(this._id))},tween:function(e,t){var n=this._id;if(e+="",arguments.length<2){for(var o,r=yn(this.node(),n).tween,i=0,a=r.length;i<a;++i)if((o=r[i]).name===e)return o.value;return null}return this.each((null==t?xn:wn)(n,e,t))},delay:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?On:An)(t,e)):yn(this.node(),t).delay},duration:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?Dn:In)(t,e)):yn(this.node(),t).duration},ease:function(e){var t=this._id;return arguments.length?this.each(function(e,t){if("function"!=typeof t)throw new Error;return function(){mn(this,e).ease=t}}(t,e)):yn(this.node(),t).ease},easeVarying:function(e){if("function"!=typeof e)throw new Error;return this.each(function(e,t){return function(){var n=t.apply(this,arguments);if("function"!=typeof n)throw new Error;mn(this,e).ease=n}}(this._id,e))},end:function(){var e,t,n=this,o=n._id,r=n.size();return new Promise((function(i,a){var s={value:a},l={value:function(){0==--r&&i()}};n.each((function(){var n=mn(this,o),r=n.on;r!==e&&((t=(e=r).copy())._.cancel.push(s),t._.interrupt.push(s),t._.end.push(l)),n.on=t})),0===r&&i()}))},[Symbol.iterator]:Bn[Symbol.iterator]};var jn={time:null,delay:0,duration:250,ease:function(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}};function Hn(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}be.prototype.interrupt=function(e){return this.each((function(){vn(this,e)}))},be.prototype.transition=function(e){var t,n;e instanceof Tn?(t=e._id,e=e._name):(t=Vn(),(n=jn).time=qt(),e=null==e?null:e+"");for(var o=this._groups,r=o.length,i=0;i<r;++i)for(var a,s=o[i],l=s.length,c=0;c<l;++c)(a=s[c])&&gn(a,e,t,c,s,n||Hn(a,t));return new Tn(o,this._parents,e,t)};var Zn=e=>()=>e;function Xn(e,{sourceEvent:t,target:n,transform:o,dispatch:r}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:o,enumerable:!0,configurable:!0},_:{value:r}})}function Yn(e,t,n){this.k=e,this.x=t,this.y=n}Yn.prototype={constructor:Yn,scale:function(e){return 1===e?this:new Yn(this.k*e,this.x,this.y)},translate:function(e,t){return 0===e&0===t?this:new Yn(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var Fn=new Yn(1,0,0);function Wn(e){for(;!e.__zoom;)if(!(e=e.parentNode))return Fn;return e.__zoom}function Kn(e){e.stopImmediatePropagation()}function Gn(e){e.preventDefault(),e.stopImmediatePropagation()}function qn(e){return!(e.ctrlKey&&"wheel"!==e.type||e.button)}function Un(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e).hasAttribute("viewBox")?[[(e=e.viewBox.baseVal).x,e.y],[e.x+e.width,e.y+e.height]]:[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]:[[0,0],[e.clientWidth,e.clientHeight]]}function Qn(){return this.__zoom||Fn}function Jn(e){return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function eo(){return navigator.maxTouchPoints||"ontouchstart"in this}function to(e,t,n){var o=e.invertX(t[0][0])-n[0][0],r=e.invertX(t[1][0])-n[1][0],i=e.invertY(t[0][1])-n[0][1],a=e.invertY(t[1][1])-n[1][1];return e.translate(r>o?(o+r)/2:Math.min(0,o)||Math.max(0,r),a>i?(i+a)/2:Math.min(0,i)||Math.max(0,a))}function no(){var e,t,n,o=qn,r=Un,i=to,s=Jn,l=eo,c=[0,1/0],u=[[-1/0,-1/0],[1/0,1/0]],d=250,h=Bt,f=a("start","zoom","end"),g=500,p=150,m=0,y=10;function v(e){e.property("__zoom",Qn).on("wheel.zoom",k,{passive:!1}).on("mousedown.zoom",M).on("dblclick.zoom",N).filter(l).on("touchstart.zoom",_).on("touchmove.zoom",P).on("touchend.zoom touchcancel.zoom",z).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function x(e,t){return(t=Math.max(c[0],Math.min(c[1],t)))===e.k?e:new Yn(t,e.x,e.y)}function w(e,t,n){var o=t[0]-n[0]*e.k,r=t[1]-n[1]*e.k;return o===e.x&&r===e.y?e:new Yn(e.k,o,r)}function b(e){return[(+e[0][0]+ +e[1][0])/2,(+e[0][1]+ +e[1][1])/2]}function S(e,t,n,o){e.on("start.zoom",(function(){C(this,arguments).event(o).start()})).on("interrupt.zoom end.zoom",(function(){C(this,arguments).event(o).end()})).tween("zoom",(function(){var e=this,i=arguments,a=C(e,i).event(o),s=r.apply(e,i),l=null==n?b(s):"function"==typeof n?n.apply(e,i):n,c=Math.max(s[1][0]-s[0][0],s[1][1]-s[0][1]),u=e.__zoom,d="function"==typeof t?t.apply(e,i):t,f=h(u.invert(l).concat(c/u.k),d.invert(l).concat(c/d.k));return function(e){if(1===e)e=d;else{var t=f(e),n=c/t[2];e=new Yn(n,l[0]-t[0]*n,l[1]-t[1]*n)}a.zoom(null,e)}}))}function C(e,t,n){return!n&&e.__zooming||new E(e,t)}function E(e,t){this.that=e,this.args=t,this.active=0,this.sourceEvent=null,this.extent=r.apply(e,t),this.taps=0}function k(e,...t){if(o.apply(this,arguments)){var n=C(this,t).event(e),r=this.__zoom,a=Math.max(c[0],Math.min(c[1],r.k*Math.pow(2,s.apply(this,arguments)))),l=Ce(e);if(n.wheel)n.mouse[0][0]===l[0]&&n.mouse[0][1]===l[1]||(n.mouse[1]=r.invert(n.mouse[0]=l)),clearTimeout(n.wheel);else{if(r.k===a)return;n.mouse=[l,r.invert(l)],vn(this),n.start()}Gn(e),n.wheel=setTimeout((function(){n.wheel=null,n.end()}),p),n.zoom("mouse",i(w(x(r,a),n.mouse[0],n.mouse[1]),n.extent,u))}}function M(e,...t){if(!n&&o.apply(this,arguments)){var r=e.currentTarget,a=C(this,t,!0).event(e),s=Se(e.view).on("mousemove.zoom",(function(e){if(Gn(e),!a.moved){var t=e.clientX-c,n=e.clientY-d;a.moved=t*t+n*n>m}a.event(e).zoom("mouse",i(w(a.that.__zoom,a.mouse[0]=Ce(e,r),a.mouse[1]),a.extent,u))}),!0).on("mouseup.zoom",(function(e){s.on("mousemove.zoom mouseup.zoom",null),Pe(e.view,a.moved),Gn(e),a.event(e).end()}),!0),l=Ce(e,r),c=e.clientX,d=e.clientY;_e(e.view),Kn(e),a.mouse=[l,this.__zoom.invert(l)],vn(this),a.start()}}function N(e,...t){if(o.apply(this,arguments)){var n=this.__zoom,a=Ce(e.changedTouches?e.changedTouches[0]:e,this),s=n.invert(a),l=n.k*(e.shiftKey?.5:2),c=i(w(x(n,l),a,s),r.apply(this,t),u);Gn(e),d>0?Se(this).transition().duration(d).call(S,c,a,e):Se(this).call(v.transform,c,a,e)}}function _(n,...r){if(o.apply(this,arguments)){var i,a,s,l,c=n.touches,u=c.length,d=C(this,r,n.changedTouches.length===u).event(n);for(Kn(n),a=0;a<u;++a)l=[l=Ce(s=c[a],this),this.__zoom.invert(l),s.identifier],d.touch0?d.touch1||d.touch0[2]===l[2]||(d.touch1=l,d.taps=0):(d.touch0=l,i=!0,d.taps=1+!!e);e&&(e=clearTimeout(e)),i&&(d.taps<2&&(t=l[0],e=setTimeout((function(){e=null}),g)),vn(this),d.start())}}function P(e,...t){if(this.__zooming){var n,o,r,a,s=C(this,t).event(e),l=e.changedTouches,c=l.length;for(Gn(e),n=0;n<c;++n)r=Ce(o=l[n],this),s.touch0&&s.touch0[2]===o.identifier?s.touch0[0]=r:s.touch1&&s.touch1[2]===o.identifier&&(s.touch1[0]=r);if(o=s.that.__zoom,s.touch1){var d=s.touch0[0],h=s.touch0[1],f=s.touch1[0],g=s.touch1[1],p=(p=f[0]-d[0])*p+(p=f[1]-d[1])*p,m=(m=g[0]-h[0])*m+(m=g[1]-h[1])*m;o=x(o,Math.sqrt(p/m)),r=[(d[0]+f[0])/2,(d[1]+f[1])/2],a=[(h[0]+g[0])/2,(h[1]+g[1])/2]}else{if(!s.touch0)return;r=s.touch0[0],a=s.touch0[1]}s.zoom("touch",i(w(o,r,a),s.extent,u))}}function z(e,...o){if(this.__zooming){var r,i,a=C(this,o).event(e),s=e.changedTouches,l=s.length;for(Kn(e),n&&clearTimeout(n),n=setTimeout((function(){n=null}),g),r=0;r<l;++r)i=s[r],a.touch0&&a.touch0[2]===i.identifier?delete a.touch0:a.touch1&&a.touch1[2]===i.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(i=Ce(i,this),Math.hypot(t[0]-i[0],t[1]-i[1])<y)){var c=Se(this).on("dblclick.zoom");c&&c.apply(this,arguments)}}}return v.transform=function(e,t,n,o){var r=e.selection?e.selection():e;r.property("__zoom",Qn),e!==r?S(e,t,n,o):r.interrupt().each((function(){C(this,arguments).event(o).start().zoom(null,"function"==typeof t?t.apply(this,arguments):t).end()}))},v.scaleBy=function(e,t,n,o){v.scaleTo(e,(function(){return this.__zoom.k*("function"==typeof t?t.apply(this,arguments):t)}),n,o)},v.scaleTo=function(e,t,n,o){v.transform(e,(function(){var e=r.apply(this,arguments),o=this.__zoom,a=null==n?b(e):"function"==typeof n?n.apply(this,arguments):n,s=o.invert(a),l="function"==typeof t?t.apply(this,arguments):t;return i(w(x(o,l),a,s),e,u)}),n,o)},v.translateBy=function(e,t,n,o){v.transform(e,(function(){return i(this.__zoom.translate("function"==typeof t?t.apply(this,arguments):t,"function"==typeof n?n.apply(this,arguments):n),r.apply(this,arguments),u)}),null,o)},v.translateTo=function(e,t,n,o,a){v.transform(e,(function(){var e=r.apply(this,arguments),a=this.__zoom,s=null==o?b(e):"function"==typeof o?o.apply(this,arguments):o;return i(Fn.translate(s[0],s[1]).scale(a.k).translate("function"==typeof t?-t.apply(this,arguments):-t,"function"==typeof n?-n.apply(this,arguments):-n),e,u)}),o,a)},E.prototype={event:function(e){return e&&(this.sourceEvent=e),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(e,t){return this.mouse&&"mouse"!==e&&(this.mouse[1]=t.invert(this.mouse[0])),this.touch0&&"touch"!==e&&(this.touch0[1]=t.invert(this.touch0[0])),this.touch1&&"touch"!==e&&(this.touch1[1]=t.invert(this.touch1[0])),this.that.__zoom=t,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(e){var t=Se(this.that).datum();f.call(e,this.that,new Xn(e,{sourceEvent:this.sourceEvent,target:v,type:e,transform:this.that.__zoom,dispatch:f}),t)}},v.wheelDelta=function(e){return arguments.length?(s="function"==typeof e?e:Zn(+e),v):s},v.filter=function(e){return arguments.length?(o="function"==typeof e?e:Zn(!!e),v):o},v.touchable=function(e){return arguments.length?(l="function"==typeof e?e:Zn(!!e),v):l},v.extent=function(e){return arguments.length?(r="function"==typeof e?e:Zn([[+e[0][0],+e[0][1]],[+e[1][0],+e[1][1]]]),v):r},v.scaleExtent=function(e){return arguments.length?(c[0]=+e[0],c[1]=+e[1],v):[c[0],c[1]]},v.translateExtent=function(e){return arguments.length?(u[0][0]=+e[0][0],u[1][0]=+e[1][0],u[0][1]=+e[0][1],u[1][1]=+e[1][1],v):[[u[0][0],u[0][1]],[u[1][0],u[1][1]]]},v.constrain=function(e){return arguments.length?(i=e,v):i},v.duration=function(e){return arguments.length?(d=+e,v):d},v.interpolate=function(e){return arguments.length?(h=e,v):h},v.on=function(){var e=f.on.apply(f,arguments);return e===f?v:e},v.clickDistance=function(e){return arguments.length?(m=(e=+e)*e,v):Math.sqrt(m)},v.tapDistance=function(e){return arguments.length?(y=+e,v):y},v}Wn.prototype=Yn.prototype;const oo={error001:()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001",error002:()=>"It looks like you've created a new nodeTypes or edgeTypes object. If this wasn't on purpose please define the nodeTypes/edgeTypes outside of the component or memoize them.",error003:e=>`Node type "${e}" not found. Using fallback type "default".`,error004:()=>"The React Flow parent container needs a width and a height to render the graph.",error005:()=>"Only child nodes can use a parent extent.",error006:()=>"Can't create edge. An edge needs a source and a target.",error007:e=>`The old edge with id=${e} does not exist.`,error009:e=>`Marker type "${e}" doesn't exist.`,error008:(e,{id:t,sourceHandle:n,targetHandle:o})=>`Couldn't create edge for ${e} handle id: "${"source"===e?n:o}", edge id: ${t}.`,error010:()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",error011:e=>`Edge type "${e}" not found. Using fallback type "default".`,error012:e=>`Node with id "${e}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`,error013:(e="react")=>`It seems that you haven't loaded the styles. Please import '@xyflow/${e}/dist/style.css' or base.css to make sure everything is working properly.`,error014:()=>"useNodeConnections: No node ID found. Call useNodeConnections inside a custom Node or provide a node ID.",error015:()=>"It seems that you are trying to drag a node that is not initialized. Please use onNodesChange as explained in the docs."},ro=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],io=["Enter"," ","Escape"],ao={"node.a11yDescription.default":"Press enter or space to select a node. Press delete to remove it and escape to cancel.","node.a11yDescription.keyboardDisabled":"Press enter or space to select a node. You can then use the arrow keys to move the node around. Press delete to remove it and escape to cancel.","node.a11yDescription.ariaLiveMessage":({direction:e,x:t,y:n})=>`Moved selected node ${e}. New position, x: ${t}, y: ${n}`,"edge.a11yDescription.default":"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel.","controls.ariaLabel":"Control Panel","controls.zoomIn.ariaLabel":"Zoom In","controls.zoomOut.ariaLabel":"Zoom Out","controls.fitView.ariaLabel":"Fit View","controls.interactive.ariaLabel":"Toggle Interactivity","minimap.ariaLabel":"Mini Map","handle.ariaLabel":"Handle"};var so,lo,co;e.ConnectionMode=void 0,(so=e.ConnectionMode||(e.ConnectionMode={})).Strict="strict",so.Loose="loose",e.PanOnScrollMode=void 0,(lo=e.PanOnScrollMode||(e.PanOnScrollMode={})).Free="free",lo.Vertical="vertical",lo.Horizontal="horizontal",e.SelectionMode=void 0,(co=e.SelectionMode||(e.SelectionMode={})).Partial="partial",co.Full="full";const uo={inProgress:!1,isValid:null,from:null,fromHandle:null,fromPosition:null,fromNode:null,to:null,toHandle:null,toPosition:null,toNode:null};var ho,fo,go;e.ConnectionLineType=void 0,(ho=e.ConnectionLineType||(e.ConnectionLineType={})).Bezier="default",ho.Straight="straight",ho.Step="step",ho.SmoothStep="smoothstep",ho.SimpleBezier="simplebezier",e.MarkerType=void 0,(fo=e.MarkerType||(e.MarkerType={})).Arrow="arrow",fo.ArrowClosed="arrowclosed",e.Position=void 0,(go=e.Position||(e.Position={})).Left="left",go.Top="top",go.Right="right",go.Bottom="bottom";const po={[e.Position.Left]:e.Position.Right,[e.Position.Right]:e.Position.Left,[e.Position.Top]:e.Position.Bottom,[e.Position.Bottom]:e.Position.Top};function mo(e,t){if(!e&&!t)return!0;if(!e||!t||e.size!==t.size)return!1;if(!e.size&&!t.size)return!0;for(const n of e.keys())if(!t.has(n))return!1;return!0}function yo(e,t,n){if(!n)return;const o=[];e.forEach(((e,n)=>{t?.has(n)||o.push(e)})),o.length&&n(o)}function vo(e){return null===e?null:e?"valid":"invalid"}const xo=e=>"id"in e&&"source"in e&&"target"in e,wo=e=>"id"in e&&"internals"in e&&!("source"in e)&&!("target"in e),bo=(e,t=[0,0])=>{const{width:n,height:o}=qo(e),r=e.origin??t,i=n*r[0],a=o*r[1];return{x:e.position.x-i,y:e.position.y-a}},So=(e,t={nodeOrigin:[0,0]})=>{if(0===e.length)return{x:0,y:0,width:0,height:0};const n=e.reduce(((e,n)=>{const o="string"==typeof n;let r=t.nodeLookup||o?void 0:n;t.nodeLookup&&(r=o?t.nodeLookup.get(n):wo(n)?n:t.nodeLookup.get(n.id));const i=r?$o(r,t.nodeOrigin):{x:0,y:0,x2:0,y2:0};return Do(e,i)}),{x:1/0,y:1/0,x2:-1/0,y2:-1/0});return Ro(n)},Co=(e,t={})=>{if(0===e.size)return{x:0,y:0,width:0,height:0};let n={x:1/0,y:1/0,x2:-1/0,y2:-1/0};return e.forEach((e=>{if(void 0===t.filter||t.filter(e)){const t=$o(e);n=Do(n,t)}})),Ro(n)},Eo=(e,t,[n,o,r]=[0,0,1],i=!1,a=!1)=>{const s={...Xo(t,[n,o,r]),width:t.width/r,height:t.height/r},l=[];for(const t of e.values()){const{measured:e,selectable:n=!0,hidden:o=!1}=t;if(a&&!n||o)continue;const r=e.width??t.width??t.initialWidth??null,c=e.height??t.height??t.initialHeight??null,u=Vo(s,Lo(t)),d=(r??0)*(c??0),h=i&&u>0;(!t.internals.handleBounds||h||u>=d||t.dragging)&&l.push(t)}return l},ko=(e,t)=>{const n=new Set;return e.forEach((e=>{n.add(e.id)})),t.filter((e=>n.has(e.source)||n.has(e.target)))};async function Mo({nodes:e,width:t,height:n,panZoom:o,minZoom:r,maxZoom:i},a){if(0===e.size)return Promise.resolve(!0);const s=function(e,t){const n=new Map,o=t?.nodes?new Set(t.nodes.map((e=>e.id))):null;return e.forEach((e=>{!e.measured.width||!e.measured.height||!t?.includeHiddenNodes&&e.hidden||o&&!o.has(e.id)||n.set(e.id,e)})),n}(e,a),l=Co(s),c=Wo(l,t,n,a?.minZoom??r,a?.maxZoom??i,a?.padding??.1);return await o.setViewport(c,{duration:a?.duration,ease:a?.ease,interpolate:a?.interpolate}),Promise.resolve(!0)}function No({nodeId:e,nextPosition:t,nodeLookup:n,nodeOrigin:o=[0,0],nodeExtent:r,onError:i}){const a=n.get(e),s=a.parentId?n.get(a.parentId):void 0,{x:l,y:c}=s?s.internals.positionAbsolute:{x:0,y:0},u=a.origin??o;let d=a.extent||r;if("parent"!==a.extent||a.expandParent)s&&Go(a.extent)&&(d=[[a.extent[0][0]+l,a.extent[0][1]+c],[a.extent[1][0]+l,a.extent[1][1]+c]]);else if(s){const e=s.measured.width,t=s.measured.height;e&&t&&(d=[[l,c],[l+e,c+t]])}else i?.("005",oo.error005());const h=Go(d)?Po(t,d,a.measured):t;return void 0!==a.measured.width&&void 0!==a.measured.height||i?.("015",oo.error015()),{position:{x:h.x-l+(a.measured.width??0)*u[0],y:h.y-c+(a.measured.height??0)*u[1]},positionAbsolute:h}}const _o=(e,t=0,n=1)=>Math.min(Math.max(e,t),n),Po=(e={x:0,y:0},t,n)=>({x:_o(e.x,t[0][0],t[1][0]-(n?.width??0)),y:_o(e.y,t[0][1],t[1][1]-(n?.height??0))});function zo(e,t,n){const{width:o,height:r}=qo(n),{x:i,y:a}=n.internals.positionAbsolute;return Po(e,[[i,a],[i+o,a+r]],t)}const Oo=(e,t,n)=>e<t?_o(Math.abs(e-t),1,t)/t:e>n?-_o(Math.abs(e-n),1,t)/t:0,Ao=(e,t,n=15,o=40)=>[Oo(e.x,o,t.width-o)*n,Oo(e.y,o,t.height-o)*n],Do=(e,t)=>({x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x2,t.x2),y2:Math.max(e.y2,t.y2)}),Io=({x:e,y:t,width:n,height:o})=>({x:e,y:t,x2:e+n,y2:t+o}),Ro=({x:e,y:t,x2:n,y2:o})=>({x:e,y:t,width:n-e,height:o-t}),Lo=(e,t=[0,0])=>{const{x:n,y:o}=wo(e)?e.internals.positionAbsolute:bo(e,t);return{x:n,y:o,width:e.measured?.width??e.width??e.initialWidth??0,height:e.measured?.height??e.height??e.initialHeight??0}},$o=(e,t=[0,0])=>{const{x:n,y:o}=wo(e)?e.internals.positionAbsolute:bo(e,t);return{x:n,y:o,x2:n+(e.measured?.width??e.width??e.initialWidth??0),y2:o+(e.measured?.height??e.height??e.initialHeight??0)}},To=(e,t)=>Ro(Do(Io(e),Io(t))),Vo=(e,t)=>{const n=Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x)),o=Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y));return Math.ceil(n*o)},Bo=e=>jo(e.width)&&jo(e.height)&&jo(e.x)&&jo(e.y),jo=e=>!isNaN(e)&&isFinite(e),Ho=(e,t)=>{},Zo=(e,t=[1,1])=>({x:t[0]*Math.round(e.x/t[0]),y:t[1]*Math.round(e.y/t[1])}),Xo=({x:e,y:t},[n,o,r],i=!1,a=[1,1])=>{const s={x:(e-n)/r,y:(t-o)/r};return i?Zo(s,a):s},Yo=({x:e,y:t},[n,o,r])=>({x:e*r+n,y:t*r+o});function Fo(e,t){if("number"==typeof e)return Math.floor(.5*(t-t/(1+e)));if("string"==typeof e&&e.endsWith("px")){const t=parseFloat(e);if(!Number.isNaN(t))return Math.floor(t)}if("string"==typeof e&&e.endsWith("%")){const n=parseFloat(e);if(!Number.isNaN(n))return Math.floor(t*n*.01)}return console.error(`[React Flow] The padding value "${e}" is invalid. Please provide a number or a string with a valid unit (px or %).`),0}const Wo=(e,t,n,o,r,i)=>{const a=function(e,t,n){if("string"==typeof e||"number"==typeof e){const o=Fo(e,n),r=Fo(e,t);return{top:o,right:r,bottom:o,left:r,x:2*r,y:2*o}}if("object"==typeof e){const o=Fo(e.top??e.y??0,n),r=Fo(e.bottom??e.y??0,n),i=Fo(e.left??e.x??0,t),a=Fo(e.right??e.x??0,t);return{top:o,right:a,bottom:r,left:i,x:i+a,y:o+r}}return{top:0,right:0,bottom:0,left:0,x:0,y:0}}(i,t,n),s=(t-a.x)/e.width,l=(n-a.y)/e.height,c=Math.min(s,l),u=_o(c,o,r),d=t/2-(e.x+e.width/2)*u,h=n/2-(e.y+e.height/2)*u,f=function(e,t,n,o,r,i){const{x:a,y:s}=Yo(e,[t,n,o]),{x:l,y:c}=Yo({x:e.x+e.width,y:e.y+e.height},[t,n,o]),u=r-l,d=i-c;return{left:Math.floor(a),top:Math.floor(s),right:Math.floor(u),bottom:Math.floor(d)}}(e,d,h,u,t,n),g=Math.min(f.left-a.left,0),p=Math.min(f.top-a.top,0);return{x:d-g+Math.min(f.right-a.right,0),y:h-p+Math.min(f.bottom-a.bottom,0),zoom:u}},Ko=()=>"undefined"!=typeof navigator&&navigator?.userAgent?.indexOf("Mac")>=0;function Go(e){return null!=e&&"parent"!==e}function qo(e){return{width:e.measured?.width??e.width??e.initialWidth??0,height:e.measured?.height??e.height??e.initialHeight??0}}function Uo(e){return void 0!==(e.measured?.width??e.width??e.initialWidth)&&void 0!==(e.measured?.height??e.height??e.initialHeight)}function Qo(e,t={width:0,height:0},n,o,r){const i={...e},a=o.get(n);if(a){const e=a.origin||r;i.x+=a.internals.positionAbsolute.x-(t.width??0)*e[0],i.y+=a.internals.positionAbsolute.y-(t.height??0)*e[1]}return i}function Jo(e,t){if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0}function er(e,{snapGrid:t=[0,0],snapToGrid:n=!1,transform:o,containerBounds:r}){const{x:i,y:a}=ar(e),s=Xo({x:i-(r?.left??0),y:a-(r?.top??0)},o),{x:l,y:c}=n?Zo(s,t):s;return{xSnapped:l,ySnapped:c,...s}}const tr=e=>({width:e.offsetWidth,height:e.offsetHeight}),nr=e=>e?.getRootNode?.()||window?.document,or=["INPUT","SELECT","TEXTAREA"];function rr(e){const t=e.composedPath?.()?.[0]||e.target;if(1!==t?.nodeType)return!1;return or.includes(t.nodeName)||t.hasAttribute("contenteditable")||!!t.closest(".nokey")}const ir=e=>"clientX"in e,ar=(e,t)=>{const n=ir(e),o=n?e.clientX:e.touches?.[0].clientX,r=n?e.clientY:e.touches?.[0].clientY;return{x:o-(t?.left??0),y:r-(t?.top??0)}},sr=(e,t,n,o,r)=>{const i=t.querySelectorAll(`.${e}`);return i&&i.length?Array.from(i).map((t=>{const i=t.getBoundingClientRect();return{id:t.getAttribute("data-handleid"),type:e,nodeId:r,position:t.getAttribute("data-handlepos"),x:(i.left-n.left)/o,y:(i.top-n.top)/o,...tr(t)}})):null};function lr({sourceX:e,sourceY:t,targetX:n,targetY:o,sourceControlX:r,sourceControlY:i,targetControlX:a,targetControlY:s}){const l=.125*e+.375*r+.375*a+.125*n,c=.125*t+.375*i+.375*s+.125*o;return[l,c,Math.abs(l-e),Math.abs(c-t)]}function cr(e,t){return e>=0?.5*e:25*t*Math.sqrt(-e)}function ur({pos:t,x1:n,y1:o,x2:r,y2:i,c:a}){switch(t){case e.Position.Left:return[n-cr(n-r,a),o];case e.Position.Right:return[n+cr(r-n,a),o];case e.Position.Top:return[n,o-cr(o-i,a)];case e.Position.Bottom:return[n,o+cr(i-o,a)]}}function dr({sourceX:t,sourceY:n,sourcePosition:o=e.Position.Bottom,targetX:r,targetY:i,targetPosition:a=e.Position.Top,curvature:s=.25}){const[l,c]=ur({pos:o,x1:t,y1:n,x2:r,y2:i,c:s}),[u,d]=ur({pos:a,x1:r,y1:i,x2:t,y2:n,c:s}),[h,f,g,p]=lr({sourceX:t,sourceY:n,targetX:r,targetY:i,sourceControlX:l,sourceControlY:c,targetControlX:u,targetControlY:d});return[`M${t},${n} C${l},${c} ${u},${d} ${r},${i}`,h,f,g,p]}function hr({sourceX:e,sourceY:t,targetX:n,targetY:o}){const r=Math.abs(n-e)/2,i=n<e?n+r:n-r,a=Math.abs(o-t)/2;return[i,o<t?o+a:o-a,r,a]}function fr({sourceNode:e,targetNode:t,width:n,height:o,transform:r}){const i=Do($o(e),$o(t));i.x===i.x2&&(i.x2+=1),i.y===i.y2&&(i.y2+=1);const a={x:-r[0]/r[2],y:-r[1]/r[2],width:n/r[2],height:o/r[2]};return Vo(a,Ro(i))>0}const gr=({source:e,sourceHandle:t,target:n,targetHandle:o})=>`xy-edge__${e}${t||""}-${n}${o||""}`,pr=(e,t)=>{if(!e.source||!e.target)return t;let n;return n=xo(e)?{...e}:{...e,id:gr(e)},((e,t)=>t.some((t=>!(t.source!==e.source||t.target!==e.target||t.sourceHandle!==e.sourceHandle&&(t.sourceHandle||e.sourceHandle)||t.targetHandle!==e.targetHandle&&(t.targetHandle||e.targetHandle)))))(n,t)?t:(null===n.sourceHandle&&delete n.sourceHandle,null===n.targetHandle&&delete n.targetHandle,t.concat(n))};function mr({sourceX:e,sourceY:t,targetX:n,targetY:o}){const[r,i,a,s]=hr({sourceX:e,sourceY:t,targetX:n,targetY:o});return[`M ${e},${t}L ${n},${o}`,r,i,a,s]}const yr={[e.Position.Left]:{x:-1,y:0},[e.Position.Right]:{x:1,y:0},[e.Position.Top]:{x:0,y:-1},[e.Position.Bottom]:{x:0,y:1}},vr=({source:t,sourcePosition:n=e.Position.Bottom,target:o})=>n===e.Position.Left||n===e.Position.Right?t.x<o.x?{x:1,y:0}:{x:-1,y:0}:t.y<o.y?{x:0,y:1}:{x:0,y:-1},xr=(e,t)=>Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2));function wr({sourceX:t,sourceY:n,sourcePosition:o=e.Position.Bottom,targetX:r,targetY:i,targetPosition:a=e.Position.Top,borderRadius:s=5,centerX:l,centerY:c,offset:u=20,stepPosition:d=.5}){const[h,f,g,p,m]=function({source:t,sourcePosition:n=e.Position.Bottom,target:o,targetPosition:r=e.Position.Top,center:i,offset:a,stepPosition:s}){const l=yr[n],c=yr[r],u={x:t.x+l.x*a,y:t.y+l.y*a},d={x:o.x+c.x*a,y:o.y+c.y*a},h=vr({source:u,sourcePosition:n,target:d}),f=0!==h.x?"x":"y",g=h[f];let p,m,y=[];const v={x:0,y:0},x={x:0,y:0},[,,w,b]=hr({sourceX:t.x,sourceY:t.y,targetX:o.x,targetY:o.y});if(l[f]*c[f]==-1){"x"===f?(p=i.x??u.x+(d.x-u.x)*s,m=i.y??(u.y+d.y)/2):(p=i.x??(u.x+d.x)/2,m=i.y??u.y+(d.y-u.y)*s);const e=[{x:p,y:u.y},{x:p,y:d.y}],t=[{x:u.x,y:m},{x:d.x,y:m}];y=l[f]===g?"x"===f?e:t:"x"===f?t:e}else{const e=[{x:u.x,y:d.y}],i=[{x:d.x,y:u.y}];if(y="x"===f?l.x===g?i:e:l.y===g?e:i,n===r){const e=Math.abs(t[f]-o[f]);if(e<=a){const n=Math.min(a-1,a-e);l[f]===g?v[f]=(u[f]>t[f]?-1:1)*n:x[f]=(d[f]>o[f]?-1:1)*n}}if(n!==r){const t="x"===f?"y":"x",n=l[f]===c[t],o=u[t]>d[t],r=u[t]<d[t];(1===l[f]&&(!n&&o||n&&r)||1!==l[f]&&(!n&&r||n&&o))&&(y="x"===f?e:i)}const s={x:u.x+v.x,y:u.y+v.y},h={x:d.x+x.x,y:d.y+x.y};Math.max(Math.abs(s.x-y[0].x),Math.abs(h.x-y[0].x))>=Math.max(Math.abs(s.y-y[0].y),Math.abs(h.y-y[0].y))?(p=(s.x+h.x)/2,m=y[0].y):(p=y[0].x,m=(s.y+h.y)/2)}return[[t,{x:u.x+v.x,y:u.y+v.y},...y,{x:d.x+x.x,y:d.y+x.y},o],p,m,w,b]}({source:{x:t,y:n},sourcePosition:o,target:{x:r,y:i},targetPosition:a,center:{x:l,y:c},offset:u,stepPosition:d});return[h.reduce(((e,t,n)=>{let o="";return o=n>0&&n<h.length-1?function(e,t,n,o){const r=Math.min(xr(e,t)/2,xr(t,n)/2,o),{x:i,y:a}=t;if(e.x===i&&i===n.x||e.y===a&&a===n.y)return`L${i} ${a}`;if(e.y===a)return`L ${i+r*(e.x<n.x?-1:1)},${a}Q ${i},${a} ${i},${a+r*(e.y<n.y?1:-1)}`;const s=e.x<n.x?1:-1;return`L ${i},${a+r*(e.y<n.y?-1:1)}Q ${i},${a} ${i+r*s},${a}`}(h[n-1],t,h[n+1],s):`${0===n?"M":"L"}${t.x} ${t.y}`,e+=o}),""),f,g,p,m]}function br(e){return e&&!(!e.internals.handleBounds&&!e.handles?.length)&&!!(e.measured.width||e.width||e.initialWidth)}function Sr(e){if(!e)return null;const t=[],n=[];for(const o of e)o.width=o.width??1,o.height=o.height??1,"source"===o.type?t.push(o):"target"===o.type&&n.push(o);return{source:t,target:n}}function Cr(t,n,o=e.Position.Left,r=!1){const i=(n?.x??0)+t.internals.positionAbsolute.x,a=(n?.y??0)+t.internals.positionAbsolute.y,{width:s,height:l}=n??qo(t);if(r)return{x:i+s/2,y:a+l/2};switch(n?.position??o){case e.Position.Top:return{x:i+s/2,y:a};case e.Position.Right:return{x:i+s,y:a+l/2};case e.Position.Bottom:return{x:i+s/2,y:a+l};case e.Position.Left:return{x:i,y:a+l/2}}}function Er(e,t){return e&&(t?e.find((e=>e.id===t)):e[0])||null}function kr(e,t){if(!e)return"";if("string"==typeof e)return e;return`${t?`${t}__`:""}${Object.keys(e).sort().map((t=>`${t}=${e[t]}`)).join("&")}`}function Mr(t,n,o,r,i){let a=.5;"start"===i?a=0:"end"===i&&(a=1);let s=[(t.x+t.width*a)*n.zoom+n.x,t.y*n.zoom+n.y-r],l=[-100*a,-100];switch(o){case e.Position.Right:s=[(t.x+t.width)*n.zoom+n.x+r,(t.y+t.height*a)*n.zoom+n.y],l=[0,-100*a];break;case e.Position.Bottom:s[1]=(t.y+t.height)*n.zoom+n.y+r,l[1]=0;break;case e.Position.Left:s=[t.x*n.zoom+n.x-r,(t.y+t.height*a)*n.zoom+n.y],l=[-100,-100*a]}return`translate(${s[0]}px, ${s[1]}px) translate(${l[0]}%, ${l[1]}%)`}const Nr={nodeOrigin:[0,0],nodeExtent:ro,elevateNodesOnSelect:!0,defaults:{}},_r={...Nr,checkEquality:!0};function Pr(e,t){const n={...e};for(const e in t)void 0!==t[e]&&(n[e]=t[e]);return n}function zr(e,t,n,o){const r=Pr(_r,o);let i=e.length>0;const a=new Map(t),s=r?.elevateNodesOnSelect?1e3:0;t.clear(),n.clear();for(const l of e){let e=a.get(l.id);if(r.checkEquality&&l===e?.internals.userNode)t.set(l.id,e);else{const n=bo(l,r.nodeOrigin),o=Go(l.extent)?l.extent:r.nodeExtent,i=Po(n,o,qo(l));e={...r.defaults,...l,measured:{width:l.measured?.width,height:l.measured?.height},internals:{positionAbsolute:i,handleBounds:l.measured?e?.internals.handleBounds:void 0,z:Ar(l,s),userNode:l}},t.set(l.id,e)}void 0!==e.measured&&void 0!==e.measured.width&&void 0!==e.measured.height||e.hidden||(i=!1),l.parentId&&Or(e,t,n,o)}return i}function Or(e,t,n,o){const{elevateNodesOnSelect:r,nodeOrigin:i,nodeExtent:a}=Pr(Nr,o),s=e.parentId,l=t.get(s);if(!l)return void console.warn(`Parent node ${s} not found. Please make sure that parent nodes are in front of their child nodes in the nodes array.`);!function(e,t){if(!e.parentId)return;const n=t.get(e.parentId);n?n.set(e.id,e):t.set(e.parentId,new Map([[e.id,e]]))}(e,n);const c=r?1e3:0,{x:u,y:d,z:h}=function(e,t,n,o,r){const{x:i,y:a}=t.internals.positionAbsolute,s=qo(e),l=bo(e,n),c=Go(e.extent)?Po(l,e.extent,s):l;let u=Po({x:i+c.x,y:a+c.y},o,s);"parent"===e.extent&&(u=zo(u,s,t));const d=Ar(e,r),h=t.internals.z??0;return{x:u.x,y:u.y,z:h>=d?h+1:d}}(e,l,i,a,c),{positionAbsolute:f}=e.internals,g=u!==f.x||d!==f.y;(g||h!==e.internals.z)&&t.set(e.id,{...e,internals:{...e.internals,positionAbsolute:g?{x:u,y:d}:f,z:h}})}function Ar(e,t){return(jo(e.zIndex)?e.zIndex:0)+(e.selected?t:0)}function Dr(e,t,n,o=[0,0]){const r=[],i=new Map;for(const n of e){const e=t.get(n.parentId);if(!e)continue;const o=i.get(n.parentId)?.expandedRect??Lo(e),r=To(o,n.rect);i.set(n.parentId,{expandedRect:r,parent:e})}return i.size>0&&i.forEach((({expandedRect:t,parent:i},a)=>{const s=i.internals.positionAbsolute,l=qo(i),c=i.origin??o,u=t.x<s.x?Math.round(Math.abs(s.x-t.x)):0,d=t.y<s.y?Math.round(Math.abs(s.y-t.y)):0,h=Math.max(l.width,Math.round(t.width)),f=Math.max(l.height,Math.round(t.height)),g=(h-l.width)*c[0],p=(f-l.height)*c[1];(u>0||d>0||g||p)&&(r.push({id:a,type:"position",position:{x:i.position.x-u+g,y:i.position.y-d+p}}),n.get(a)?.forEach((t=>{e.some((e=>e.id===t.id))||r.push({id:t.id,type:"position",position:{x:t.position.x+u,y:t.position.y+d}})}))),(l.width<t.width||l.height<t.height||u||d)&&r.push({id:a,type:"dimensions",setAttributes:!0,dimensions:{width:h+(u?c[0]*u-g:0),height:f+(d?c[1]*d-p:0)}})})),r}function Ir(e,t,n,o,r,i){let a=r;const s=o.get(a)||new Map;o.set(a,s.set(n,t)),a=`${r}-${e}`;const l=o.get(a)||new Map;if(o.set(a,l.set(n,t)),i){a=`${r}-${e}-${i}`;const s=o.get(a)||new Map;o.set(a,s.set(n,t))}}function Rr(e,t,n){e.clear(),t.clear();for(const o of n){const{source:n,target:r,sourceHandle:i=null,targetHandle:a=null}=o,s={edgeId:o.id,source:n,target:r,sourceHandle:i,targetHandle:a},l=`${n}-${i}--${r}-${a}`;Ir("source",s,`${r}-${a}--${n}-${i}`,e,n,i),Ir("target",s,l,e,r,a),t.set(o.id,o)}}function Lr(e,t){if(null===e||null===t)return!1;const n=Array.isArray(e)?e:[e],o=Array.isArray(t)?t:[t];if(n.length!==o.length)return!1;for(let e=0;e<n.length;e++)if(n[e].id!==o[e].id||n[e].type!==o[e].type||!Object.is(n[e].data,o[e].data))return!1;return!0}function $r(e,t){if(!e.parentId)return!1;const n=t.get(e.parentId);return!!n&&(!!n.selected||$r(n,t))}function Tr(e,t,n){let o=e;do{if(o?.matches?.(t))return!0;if(o===n)return!1;o=o?.parentElement}while(o);return!1}function Vr({nodeId:e,dragItems:t,nodeLookup:n,dragging:o=!0}){const r=[];for(const[e,i]of t){const t=n.get(e)?.internals.userNode;t&&r.push({...t,position:i.position,dragging:o})}if(!e)return[r[0],r];const i=n.get(e)?.internals.userNode;return[i?{...i,position:t.get(e)?.position||i.position,dragging:o}:r[0],r]}function Br({onNodeMouseDown:e,getStoreItems:t,onDragStart:n,onDrag:o,onDragStop:r}){let i={x:null,y:null},a=0,s=new Map,l=!1,c={x:0,y:0},u=null,d=!1,h=null,f=!1,g=!1,p=null;return{update:function({noDragClassName:m,handleSelector:y,domNode:v,isSelectable:x,nodeId:w,nodeClickDistance:b=0}){function S({x:e,y:n}){const{nodeLookup:r,nodeExtent:a,snapGrid:l,snapToGrid:c,nodeOrigin:u,onNodeDrag:d,onSelectionDrag:h,onError:f,updateNodePositions:m}=t();i={x:e,y:n};let y=!1;const v=s.size>1,x=v&&a?Io(Co(s)):null,b=v&&c?function({dragItems:e,snapGrid:t,x:n,y:o}){const r=e.values().next().value;if(!r)return null;const i={x:n-r.distance.x,y:o-r.distance.y},a=Zo(i,t);return{x:a.x-i.x,y:a.y-i.y}}({dragItems:s,snapGrid:l,x:e,y:n}):null;for(const[t,o]of s){if(!r.has(t))continue;let i={x:e-o.distance.x,y:n-o.distance.y};c&&(i=b?{x:Math.round(i.x+b.x),y:Math.round(i.y+b.y)}:Zo(i,l));let s=null;if(v&&a&&!o.extent&&x){const{positionAbsolute:e}=o.internals,t=e.x-x.x+a[0][0],n=e.x+o.measured.width-x.x2+a[1][0];s=[[t,e.y-x.y+a[0][1]],[n,e.y+o.measured.height-x.y2+a[1][1]]]}const{position:d,positionAbsolute:h}=No({nodeId:t,nextPosition:i,nodeLookup:r,nodeExtent:s||a,nodeOrigin:u,onError:f});y=y||o.position.x!==d.x||o.position.y!==d.y,o.position=d,o.internals.positionAbsolute=h}if(g=g||y,y&&(m(s,!0),p&&(o||d||!w&&h))){const[e,t]=Vr({nodeId:w,dragItems:s,nodeLookup:r});o?.(p,s,e,t),d?.(p,e,t),w||h?.(p,t)}}async function C(){if(!u)return;const{transform:e,panBy:n,autoPanSpeed:o,autoPanOnNodeDrag:r}=t();if(!r)return l=!1,void cancelAnimationFrame(a);const[s,d]=Ao(c,u,o);0===s&&0===d||(i.x=(i.x??0)-s/e[2],i.y=(i.y??0)-d/e[2],await n({x:s,y:d})&&S(i)),a=requestAnimationFrame(C)}function E(o){const{nodeLookup:r,multiSelectionActive:a,nodesDraggable:l,transform:c,snapGrid:h,snapToGrid:f,selectNodesOnDrag:g,onNodeDragStart:p,onSelectionDragStart:m,unselectNodesAndEdges:y}=t();d=!0,g&&x||a||!w||r.get(w)?.selected||y(),x&&g&&w&&e?.(w);const v=er(o.sourceEvent,{transform:c,snapGrid:h,snapToGrid:f,containerBounds:u});if(i=v,s=function(e,t,n,o){const r=new Map;for(const[i,a]of e)if((a.selected||a.id===o)&&(!a.parentId||!$r(a,e))&&(a.draggable||t&&void 0===a.draggable)){const t=e.get(i);t&&r.set(i,{id:i,position:t.position||{x:0,y:0},distance:{x:n.x-t.internals.positionAbsolute.x,y:n.y-t.internals.positionAbsolute.y},extent:t.extent,parentId:t.parentId,origin:t.origin,expandParent:t.expandParent,internals:{positionAbsolute:t.internals.positionAbsolute||{x:0,y:0}},measured:{width:t.measured.width??0,height:t.measured.height??0}})}return r}(r,l,v,w),s.size>0&&(n||p||!w&&m)){const[e,t]=Vr({nodeId:w,dragItems:s,nodeLookup:r});n?.(o.sourceEvent,s,e,t),p?.(o.sourceEvent,e,t),w||m?.(o.sourceEvent,t)}}h=Se(v);const k=Le().clickDistance(b).on("start",(e=>{const{domNode:n,nodeDragThreshold:o,transform:r,snapGrid:a,snapToGrid:s}=t();u=n?.getBoundingClientRect()||null,f=!1,g=!1,p=e.sourceEvent,0===o&&E(e);const l=er(e.sourceEvent,{transform:r,snapGrid:a,snapToGrid:s,containerBounds:u});i=l,c=ar(e.sourceEvent,u)})).on("drag",(e=>{const{autoPanOnNodeDrag:n,transform:o,snapGrid:r,snapToGrid:a,nodeDragThreshold:h,nodeLookup:g}=t(),m=er(e.sourceEvent,{transform:o,snapGrid:r,snapToGrid:a,containerBounds:u});if(p=e.sourceEvent,("touchmove"===e.sourceEvent.type&&e.sourceEvent.touches.length>1||w&&!g.has(w))&&(f=!0),!f){if(!l&&n&&d&&(l=!0,C()),!d){const t=m.xSnapped-(i.x??0),n=m.ySnapped-(i.y??0);Math.sqrt(t*t+n*n)>h&&E(e)}(i.x!==m.xSnapped||i.y!==m.ySnapped)&&s&&d&&(c=ar(e.sourceEvent,u),S(m))}})).on("end",(e=>{if(d&&!f&&(l=!1,d=!1,cancelAnimationFrame(a),s.size>0)){const{nodeLookup:n,updateNodePositions:o,onNodeDragStop:i,onSelectionDragStop:a}=t();if(g&&(o(s,!1),g=!1),r||i||!w&&a){const[t,o]=Vr({nodeId:w,dragItems:s,nodeLookup:n,dragging:!1});r?.(e.sourceEvent,s,t,o),i?.(e.sourceEvent,t,o),w||a?.(e.sourceEvent,o)}}})).filter((e=>{const t=e.target;return!e.button&&(!m||!Tr(t,`.${m}`,v))&&(!y||Tr(t,y,v))}));h.call(k)},destroy:function(){h?.on(".drag",null)}}}const jr=250;function Hr(e,t,n,o){let r=[],i=1/0;const a=function(e,t,n){const o=[],r={x:e.x-n,y:e.y-n,width:2*n,height:2*n};for(const e of t.values())Vo(r,Lo(e))>0&&o.push(e);return o}(e,n,t+jr);for(const n of a){const a=[...n.internals.handleBounds?.source??[],...n.internals.handleBounds?.target??[]];for(const s of a){if(o.nodeId===s.nodeId&&o.type===s.type&&o.id===s.id)continue;const{x:a,y:l}=Cr(n,s,s.position,!0),c=Math.sqrt(Math.pow(a-e.x,2)+Math.pow(l-e.y,2));c>t||(c<i?(r=[{...s,x:a,y:l}],i=c):c===i&&r.push({...s,x:a,y:l}))}}if(!r.length)return null;if(r.length>1){const e="source"===o.type?"target":"source";return r.find((t=>t.type===e))??r[0]}return r[0]}function Zr(e,t,n,o,r,i=!1){const a=o.get(e);if(!a)return null;const s="strict"===r?a.internals.handleBounds?.[t]:[...a.internals.handleBounds?.source??[],...a.internals.handleBounds?.target??[]],l=(n?s?.find((e=>e.id===n)):s?.[0])??null;return l&&i?{...l,...Cr(a,l,l.position,!0)}:l}function Xr(e,t){return e||(t?.classList.contains("target")?"target":t?.classList.contains("source")?"source":null)}const Yr=()=>!0;function Fr(t,{handle:n,connectionMode:o,fromNodeId:r,fromHandleId:i,fromType:a,doc:s,lib:l,flowId:c,isValidConnection:u=Yr,nodeLookup:d}){const h="target"===a,f=n?s.querySelector(`.${l}-flow__handle[data-id="${c}-${n?.nodeId}-${n?.id}-${n?.type}"]`):null,{x:g,y:p}=ar(t),m=s.elementFromPoint(g,p),y=m?.classList.contains(`${l}-flow__handle`)?m:f,v={handleDomNode:y,isValid:!1,connection:null,toHandle:null};if(y){const t=Xr(void 0,y),n=y.getAttribute("data-nodeid"),a=y.getAttribute("data-handleid"),s=y.classList.contains("connectable"),l=y.classList.contains("connectableend");if(!n||!t)return v;const c={source:h?n:r,sourceHandle:h?a:i,target:h?r:n,targetHandle:h?i:a};v.connection=c;const f=s&&l&&(o===e.ConnectionMode.Strict?h&&"source"===t||!h&&"target"===t:n!==r||a!==i);v.isValid=f&&u(c),v.toHandle=Zr(n,t,a,d,o,!0)}return v}const Wr={onPointerDown:function(t,{connectionMode:n,connectionRadius:o,handleId:r,nodeId:i,edgeUpdaterType:a,isTarget:s,domNode:l,nodeLookup:c,lib:u,autoPanOnConnect:d,flowId:h,panBy:f,cancelConnection:g,onConnectStart:p,onConnect:m,onConnectEnd:y,isValidConnection:v=Yr,onReconnectEnd:x,updateConnection:w,getTransform:b,getFromHandle:S,autoPanSpeed:C,dragThreshold:E=1,handleDomNode:k}){const M=nr(t.target);let N,_=0;const{x:P,y:z}=ar(t),O=Xr(a,k),A=l?.getBoundingClientRect();let D=!1;if(!A||!O)return;const I=Zr(i,O,r,c,n);if(!I)return;let R=ar(t,A),L=!1,$=null,T=!1,V=null;function B(){if(!d||!A)return;const[e,t]=Ao(R,A,C);f({x:e,y:t}),_=requestAnimationFrame(B)}const j={...I,nodeId:i,type:O,position:I.position},H=c.get(i);let Z={inProgress:!0,isValid:null,from:Cr(H,j,e.Position.Left,!0),fromHandle:j,fromPosition:j.position,fromNode:H,to:R,toHandle:null,toPosition:po[j.position],toNode:null};function X(){D=!0,w(Z),p?.(t,{nodeId:i,handleId:r,handleType:O})}function Y(e){if(!D){const{x:t,y:n}=ar(e),o=t-P,r=n-z;if(!(o*o+r*r>E*E))return;X()}if(!S()||!j)return void F(e);const t=b();R=ar(e,A),N=Hr(Xo(R,t,!1,[1,1]),o,c,j),L||(B(),L=!0);const a=Fr(e,{handle:N,connectionMode:n,fromNodeId:i,fromHandleId:r,fromType:s?"target":"source",isValidConnection:v,doc:M,lib:u,flowId:h,nodeLookup:c});V=a.handleDomNode,$=a.connection,T=function(e,t){let n=null;return t?n=!0:e&&!t&&(n=!1),n}(!!N,a.isValid);const l={...Z,isValid:T,to:a.toHandle&&T?Yo({x:a.toHandle.x,y:a.toHandle.y},t):R,toHandle:a.toHandle,toPosition:T&&a.toHandle?a.toHandle.position:po[j.position],toNode:a.toHandle?c.get(a.toHandle.nodeId):null};T&&N&&Z.toHandle&&l.toHandle&&Z.toHandle.type===l.toHandle.type&&Z.toHandle.nodeId===l.toHandle.nodeId&&Z.toHandle.id===l.toHandle.id&&Z.to.x===l.to.x&&Z.to.y===l.to.y||(w(l),Z=l)}function F(e){if(D){(N||V)&&$&&T&&m?.($);const{inProgress:t,...n}=Z,o={...n,toPosition:Z.toHandle?Z.toPosition:null};y?.(e,o),a&&x?.(e,o)}g(),cancelAnimationFrame(_),L=!1,T=!1,$=null,V=null,M.removeEventListener("mousemove",Y),M.removeEventListener("mouseup",F),M.removeEventListener("touchmove",Y),M.removeEventListener("touchend",F)}0===E&&X(),M.addEventListener("mousemove",Y),M.addEventListener("mouseup",F),M.addEventListener("touchmove",Y),M.addEventListener("touchend",F)},isValid:Fr};const Kr=(e,t)=>e.x!==t.x||e.y!==t.y||e.zoom!==t.k,Gr=e=>({x:e.x,y:e.y,zoom:e.k}),qr=({x:e,y:t,zoom:n})=>Fn.translate(e,t).scale(n),Ur=(e,t)=>e.target.closest(`.${t}`),Qr=(e,t)=>2===t&&Array.isArray(e)&&e.includes(2),Jr=e=>((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2,ei=(e,t=0,n=Jr,o=(()=>{}))=>{const r="number"==typeof t&&t>0;return r||o(),r?e.transition().duration(t).ease(n).on("end",o):e},ti=e=>{const t=e.ctrlKey&&Ko()?10:1;return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*t};function ni({domNode:t,minZoom:n,maxZoom:o,paneClickDistance:r,translateExtent:i,viewport:a,onPanZoom:s,onPanZoomStart:l,onPanZoomEnd:c,onDraggingChange:u}){const d={isZoomingOrPanning:!1,usedRightMouseButton:!1,prevViewport:{x:0,y:0,zoom:0},mouseButton:0,timerId:void 0,panScrollTimeout:void 0,isPanScrolling:!1},h=t.getBoundingClientRect(),f=no().clickDistance(!jo(r)||r<0?0:r).scaleExtent([n,o]).translateExtent(i),g=Se(t).call(f);x({x:a.x,y:a.y,zoom:_o(a.zoom,n,o)},[[0,0],[h.width,h.height]],i);const p=g.on("wheel.zoom"),m=g.on("dblclick.zoom");function y(e,t){return g?new Promise((n=>{f?.interpolate("linear"===t?.interpolate?Pt:Bt).transform(ei(g,t?.duration,t?.ease,(()=>n(!0))),e)})):Promise.resolve(!1)}function v(){f.on("zoom",null)}async function x(e,t,n){const o=qr(e),r=f?.constrain()(o,t,n);return r&&await y(r),new Promise((e=>e(r)))}return f.wheelDelta(ti),{update:function({noWheelClassName:t,noPanClassName:n,onPaneContextMenu:o,userSelectionActive:r,panOnScroll:i,panOnDrag:a,panOnScrollMode:h,panOnScrollSpeed:y,preventScrolling:x,zoomOnPinch:w,zoomOnScroll:b,zoomOnDoubleClick:S,zoomActivationKeyPressed:C,lib:E,onTransformChange:k}){r&&!d.isZoomingOrPanning&&v();const M=i&&!C&&!r?function({zoomPanValues:t,noWheelClassName:n,d3Selection:o,d3Zoom:r,panOnScrollMode:i,panOnScrollSpeed:a,zoomOnPinch:s,onPanZoomStart:l,onPanZoom:c,onPanZoomEnd:u}){return d=>{if(Ur(d,n))return!1;d.preventDefault(),d.stopImmediatePropagation();const h=o.property("__zoom").k||1;if(d.ctrlKey&&s){const e=Ce(d),t=ti(d),n=h*Math.pow(2,t);return void r.scaleTo(o,n,e,d)}const f=1===d.deltaMode?20:1;let g=i===e.PanOnScrollMode.Vertical?0:d.deltaX*f,p=i===e.PanOnScrollMode.Horizontal?0:d.deltaY*f;!Ko()&&d.shiftKey&&i!==e.PanOnScrollMode.Vertical&&(g=d.deltaY*f,p=0),r.translateBy(o,-g/h*a,-p/h*a,{internal:!0});const m=Gr(o.property("__zoom"));clearTimeout(t.panScrollTimeout),t.isPanScrolling||(t.isPanScrolling=!0,l?.(d,m)),t.isPanScrolling&&(c?.(d,m),t.panScrollTimeout=setTimeout((()=>{u?.(d,m),t.isPanScrolling=!1}),150))}}({zoomPanValues:d,noWheelClassName:t,d3Selection:g,d3Zoom:f,panOnScrollMode:h,panOnScrollSpeed:y,zoomOnPinch:w,onPanZoomStart:l,onPanZoom:s,onPanZoomEnd:c}):function({noWheelClassName:e,preventScrolling:t,d3ZoomHandler:n}){return function(o,r){const i="wheel"===o.type,a=!t&&i&&!o.ctrlKey,s=Ur(o,e);if(o.ctrlKey&&i&&s&&o.preventDefault(),a||s)return null;o.preventDefault(),n.call(this,o,r)}}({noWheelClassName:t,preventScrolling:x,d3ZoomHandler:p});if(g.on("wheel.zoom",M,{passive:!1}),!r){const e=function({zoomPanValues:e,onDraggingChange:t,onPanZoomStart:n}){return o=>{if(o.sourceEvent?.internal)return;const r=Gr(o.transform);e.mouseButton=o.sourceEvent?.button||0,e.isZoomingOrPanning=!0,e.prevViewport=r,"mousedown"===o.sourceEvent?.type&&t(!0),n&&n?.(o.sourceEvent,r)}}({zoomPanValues:d,onDraggingChange:u,onPanZoomStart:l});f.on("start",e);const t=function({zoomPanValues:e,panOnDrag:t,onPaneContextMenu:n,onTransformChange:o,onPanZoom:r}){return i=>{e.usedRightMouseButton=!(!n||!Qr(t,e.mouseButton??0)),i.sourceEvent?.sync||o([i.transform.x,i.transform.y,i.transform.k]),r&&!i.sourceEvent?.internal&&r?.(i.sourceEvent,Gr(i.transform))}}({zoomPanValues:d,panOnDrag:a,onPaneContextMenu:!!o,onPanZoom:s,onTransformChange:k});f.on("zoom",t);const n=function({zoomPanValues:e,panOnDrag:t,panOnScroll:n,onDraggingChange:o,onPanZoomEnd:r,onPaneContextMenu:i}){return a=>{if(!a.sourceEvent?.internal&&(e.isZoomingOrPanning=!1,i&&Qr(t,e.mouseButton??0)&&!e.usedRightMouseButton&&a.sourceEvent&&i(a.sourceEvent),e.usedRightMouseButton=!1,o(!1),r&&Kr(e.prevViewport,a.transform))){const t=Gr(a.transform);e.prevViewport=t,clearTimeout(e.timerId),e.timerId=setTimeout((()=>{r?.(a.sourceEvent,t)}),n?150:0)}}}({zoomPanValues:d,panOnDrag:a,panOnScroll:i,onPaneContextMenu:o,onPanZoomEnd:c,onDraggingChange:u});f.on("end",n)}const N=function({zoomActivationKeyPressed:e,zoomOnScroll:t,zoomOnPinch:n,panOnDrag:o,panOnScroll:r,zoomOnDoubleClick:i,userSelectionActive:a,noWheelClassName:s,noPanClassName:l,lib:c}){return u=>{const d=e||t,h=n&&u.ctrlKey;if(1===u.button&&"mousedown"===u.type&&(Ur(u,`${c}-flow__node`)||Ur(u,`${c}-flow__edge`)))return!0;if(!(o||d||r||i||n))return!1;if(a)return!1;if(Ur(u,s)&&"wheel"===u.type)return!1;if(Ur(u,l)&&("wheel"!==u.type||r&&"wheel"===u.type&&!e))return!1;if(!n&&u.ctrlKey&&"wheel"===u.type)return!1;if(!n&&"touchstart"===u.type&&u.touches?.length>1)return u.preventDefault(),!1;if(!d&&!r&&!h&&"wheel"===u.type)return!1;if(!o&&("mousedown"===u.type||"touchstart"===u.type))return!1;if(Array.isArray(o)&&!o.includes(u.button)&&"mousedown"===u.type)return!1;const f=Array.isArray(o)&&o.includes(u.button)||!u.button||u.button<=1;return(!u.ctrlKey||"wheel"===u.type)&&f}}({zoomActivationKeyPressed:C,panOnDrag:a,zoomOnScroll:b,panOnScroll:i,zoomOnDoubleClick:S,zoomOnPinch:w,userSelectionActive:r,noPanClassName:n,noWheelClassName:t,lib:E});f.filter(N),S?g.on("dblclick.zoom",m):g.on("dblclick.zoom",null)},destroy:v,setViewport:async function(e,t){const n=qr(e);return await y(n,t),new Promise((e=>e(n)))},setViewportConstrained:x,getViewport:function(){const e=g?Wn(g.node()):{x:0,y:0,k:1};return{x:e.x,y:e.y,zoom:e.k}},scaleTo:function(e,t){return g?new Promise((n=>{f?.interpolate("linear"===t?.interpolate?Pt:Bt).scaleTo(ei(g,t?.duration,t?.ease,(()=>n(!0))),e)})):Promise.resolve(!1)},scaleBy:function(e,t){return g?new Promise((n=>{f?.interpolate("linear"===t?.interpolate?Pt:Bt).scaleBy(ei(g,t?.duration,t?.ease,(()=>n(!0))),e)})):Promise.resolve(!1)},setScaleExtent:function(e){f?.scaleExtent(e)},setTranslateExtent:function(e){f?.translateExtent(e)},syncViewport:function(e){if(g){const t=qr(e),n=g.property("__zoom");n.k===e.zoom&&n.x===e.x&&n.y===e.y||f?.transform(g,t,null,{sync:!0})}},setClickDistance:function(e){const t=!jo(e)||e<0?0:e;f?.clickDistance(t)}}}var oi;e.ResizeControlVariant=void 0,(oi=e.ResizeControlVariant||(e.ResizeControlVariant={})).Line="line",oi.Handle="handle";const ri=["top-left","top-right","bottom-left","bottom-right"],ii=["top","right","bottom","left"];function ai(e,t){return Math.max(0,t-e)}function si(e,t){return Math.max(0,e-t)}function li(e,t,n){return Math.max(0,t-e,e-n)}function ci(e,t){return e?!t:t}const ui={width:0,height:0,x:0,y:0},di={...ui,pointerX:0,pointerY:0,aspectRatio:1};function hi(e,t,n){const o=t.position.x+e.position.x,r=t.position.y+e.position.y,i=e.measured.width??0,a=e.measured.height??0,s=n[0]*i,l=n[1]*a;return[[o-s,r-l],[o+i-s,r+a-l]]}function fi({domNode:e,nodeId:t,getStoreItems:n,onChange:o,onEnd:r}){const i=Se(e);return{update:function({controlPosition:e,boundaries:a,keepAspectRatio:s,resizeDirection:l,onResizeStart:c,onResize:u,onResizeEnd:d,shouldResize:h}){let f={...ui},g={...di};const p=function(e){return{isHorizontal:e.includes("right")||e.includes("left"),isVertical:e.includes("bottom")||e.includes("top"),affectsX:e.includes("left"),affectsY:e.includes("top")}}(e);let m,y,v,x,w=null,b=[];const S=Le().on("start",(e=>{const{nodeLookup:o,transform:r,snapGrid:i,snapToGrid:a,nodeOrigin:s,paneDomNode:l}=n();if(m=o.get(t),!m)return;w=l?.getBoundingClientRect()??null;const{xSnapped:u,ySnapped:d}=er(e.sourceEvent,{transform:r,snapGrid:i,snapToGrid:a,containerBounds:w});f={width:m.measured.width??0,height:m.measured.height??0,x:m.position.x??0,y:m.position.y??0},g={...f,pointerX:u,pointerY:d,aspectRatio:f.width/f.height},y=void 0,m.parentId&&("parent"===m.extent||m.expandParent)&&(y=o.get(m.parentId),v=y&&"parent"===m.extent?function(e){return[[0,0],[e.measured.width,e.measured.height]]}(y):void 0),b=[],x=void 0;for(const[e,n]of o)if(n.parentId===t&&(b.push({id:e,position:{...n.position},extent:n.extent}),"parent"===n.extent||n.expandParent)){const e=hi(n,m,n.origin??s);x=x?[[Math.min(e[0][0],x[0][0]),Math.min(e[0][1],x[0][1])],[Math.max(e[1][0],x[1][0]),Math.max(e[1][1],x[1][1])]]:e}c?.(e,{...f})})).on("drag",(e=>{const{transform:t,snapGrid:r,snapToGrid:i,nodeOrigin:c}=n(),d=er(e.sourceEvent,{transform:t,snapGrid:r,snapToGrid:i,containerBounds:w}),S=[];if(!m)return;const{x:C,y:E,width:k,height:M}=f,N={},_=m.origin??c,{width:P,height:z,x:O,y:A}=function(e,t,n,o,r,i,a,s){let{affectsX:l,affectsY:c}=t;const{isHorizontal:u,isVertical:d}=t,h=u&&d,{xSnapped:f,ySnapped:g}=n,{minWidth:p,maxWidth:m,minHeight:y,maxHeight:v}=o,{x:x,y:w,width:b,height:S,aspectRatio:C}=e;let E=Math.floor(u?f-e.pointerX:0),k=Math.floor(d?g-e.pointerY:0);const M=b+(l?-E:E),N=S+(c?-k:k),_=-i[0]*b,P=-i[1]*S;let z=li(M,p,m),O=li(N,y,v);if(a){let e=0,t=0;l&&E<0?e=ai(x+E+_,a[0][0]):!l&&E>0&&(e=si(x+M+_,a[1][0])),c&&k<0?t=ai(w+k+P,a[0][1]):!c&&k>0&&(t=si(w+N+P,a[1][1])),z=Math.max(z,e),O=Math.max(O,t)}if(s){let e=0,t=0;l&&E>0?e=si(x+E,s[0][0]):!l&&E<0&&(e=ai(x+M,s[1][0])),c&&k>0?t=si(w+k,s[0][1]):!c&&k<0&&(t=ai(w+N,s[1][1])),z=Math.max(z,e),O=Math.max(O,t)}if(r){if(u){const e=li(M/C,y,v)*C;if(z=Math.max(z,e),a){let e=0;e=!l&&!c||l&&!c&&h?si(w+P+M/C,a[1][1])*C:ai(w+P+(l?E:-E)/C,a[0][1])*C,z=Math.max(z,e)}if(s){let e=0;e=!l&&!c||l&&!c&&h?ai(w+M/C,s[1][1])*C:si(w+(l?E:-E)/C,s[0][1])*C,z=Math.max(z,e)}}if(d){const e=li(N*C,p,m)/C;if(O=Math.max(O,e),a){let e=0;e=!l&&!c||c&&!l&&h?si(x+N*C+_,a[1][0])/C:ai(x+(c?k:-k)*C+_,a[0][0])/C,O=Math.max(O,e)}if(s){let e=0;e=!l&&!c||c&&!l&&h?ai(x+N*C,s[1][0])/C:si(x+(c?k:-k)*C,s[0][0])/C,O=Math.max(O,e)}}}k+=k<0?O:-O,E+=E<0?z:-z,r&&(h?M>N*C?k=(ci(l,c)?-E:E)/C:E=(ci(l,c)?-k:k)*C:u?(k=E/C,c=l):(E=k*C,l=c));const A=l?x+E:x,D=c?w+k:w;return{width:b+(l?-E:E),height:S+(c?-k:k),x:i[0]*E*(l?-1:1)+A,y:i[1]*k*(c?-1:1)+D}}(g,p,d,a,s,_,v,x),D=P!==k,I=z!==M,R=O!==C&&D,L=A!==E&&I;if(!(R||L||D||I))return;if((R||L||1===_[0]||1===_[1])&&(N.x=R?O:f.x,N.y=L?A:f.y,f.x=N.x,f.y=N.y,b.length>0)){const e=O-C,t=A-E;for(const n of b)n.position={x:n.position.x-e+_[0]*(P-k),y:n.position.y-t+_[1]*(z-M)},S.push(n)}if((D||I)&&(N.width=!D||l&&"horizontal"!==l?f.width:P,N.height=!I||l&&"vertical"!==l?f.height:z,f.width=N.width,f.height=N.height),y&&m.expandParent){const e=_[0]*(N.width??0);N.x&&N.x<e&&(f.x=e,g.x=g.x-(N.x-e));const t=_[1]*(N.height??0);N.y&&N.y<t&&(f.y=t,g.y=g.y-(N.y-t))}const $=function({width:e,prevWidth:t,height:n,prevHeight:o,affectsX:r,affectsY:i}){const a=e-t,s=n-o,l=[a>0?1:a<0?-1:0,s>0?1:s<0?-1:0];return a&&r&&(l[0]=-1*l[0]),s&&i&&(l[1]=-1*l[1]),l}({width:f.width,prevWidth:k,height:f.height,prevHeight:M,affectsX:p.affectsX,affectsY:p.affectsY}),T={...f,direction:$},V=h?.(e,T);!1!==V&&(u?.(e,T),o(N,S))})).on("end",(e=>{d?.(e,{...f}),r?.({...f})}));i.call(S)},destroy:function(){i.on(".drag",null)}}}function gi(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var pi,mi,yi,vi={exports:{}},xi={},wi={exports:{}},bi={};function Si(){return mi||(mi=1,wi.exports=function(){if(pi)return bi;pi=1;var e=n,t="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=e.useState,r=e.useEffect,i=e.useLayoutEffect,a=e.useDebugValue;function s(e){var n=e.getSnapshot;e=e.value;try{var o=n();return!t(e,o)}catch(e){return!0}}var l="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),l=o({inst:{value:n,getSnapshot:t}}),c=l[0].inst,u=l[1];return i((function(){c.value=n,c.getSnapshot=t,s(c)&&u({inst:c})}),[e,n,t]),r((function(){return s(c)&&u({inst:c}),e((function(){s(c)&&u({inst:c})}))}),[e]),a(n),n};return bi.useSyncExternalStore=void 0!==e.useSyncExternalStore?e.useSyncExternalStore:l,bi}()),wi.exports}
/**
   * @license React
   * use-sync-external-store-shim/with-selector.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */vi.exports=function(){if(yi)return xi;yi=1;var e=n,t=Si(),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},r=t.useSyncExternalStore,i=e.useRef,a=e.useEffect,s=e.useMemo,l=e.useDebugValue;return xi.useSyncExternalStoreWithSelector=function(e,t,n,c,u){var d=i(null);if(null===d.current){var h={hasValue:!1,value:null};d.current=h}else h=d.current;d=s((function(){function e(e){if(!a){if(a=!0,r=e,e=c(e),void 0!==u&&h.hasValue){var t=h.value;if(u(t,e))return i=t}return i=e}if(t=i,o(r,e))return t;var n=c(e);return void 0!==u&&u(t,n)?t:(r=e,i=n)}var r,i,a=!1,s=void 0===n?null:n;return[function(){return e(t())},null===s?void 0:function(){return e(s())}]}),[t,n,c,u]);var f=r(e,d[0],d[1]);return a((function(){h.hasValue=!0,h.value=f}),[f]),l(f),f},xi}();var Ci=gi(vi.exports);const Ei=e=>{let t;const n=new Set,o=(e,o)=>{const r="function"==typeof e?e(t):e;if(!Object.is(r,t)){const e=t;t=(null!=o?o:"object"!=typeof r)?r:Object.assign({},t,r),n.forEach((n=>n(t,e)))}},r=()=>t,i={setState:o,getState:r,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}};return t=e(o,r,i),i},{useSyncExternalStoreWithSelector:ki}=Ci;function Mi(e,t=e.getState,o){const r=ki(e.subscribe,e.getState,e.getServerState||e.getState,t,o);return n.useDebugValue(r),r}const Ni=(e,t)=>{const n=(e=>e?Ei(e):Ei)(e),o=(e,o=t)=>Mi(n,e,o);return Object.assign(o,n),o},_i=n.createContext(null),Pi=_i.Provider,zi=oo.error001();function Oi(e,t){const o=n.useContext(_i);if(null===o)throw new Error(zi);return Mi(o,e,t)}function Ai(){const e=n.useContext(_i);if(null===e)throw new Error(zi);return n.useMemo((()=>({getState:e.getState,setState:e.setState,subscribe:e.subscribe})),[e])}const Di={display:"none"},Ii={position:"absolute",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)",clipPath:"inset(100%)"},Ri="react-flow__node-desc",Li="react-flow__edge-desc",$i="react-flow__aria-live",Ti=e=>e.ariaLiveMessage,Vi=e=>e.ariaLabelConfig;function Bi({rfId:e}){const n=Oi(Ti);return t.jsx("div",{id:`${$i}-${e}`,"aria-live":"assertive","aria-atomic":"true",style:Ii,children:n})}function ji({rfId:e,disableKeyboardA11y:n}){const o=Oi(Vi);return t.jsxs(t.Fragment,{children:[t.jsx("div",{id:`${Ri}-${e}`,style:Di,children:n?o["node.a11yDescription.default"]:o["node.a11yDescription.keyboardDisabled"]}),t.jsx("div",{id:`${Li}-${e}`,style:Di,children:o["edge.a11yDescription.default"]}),!n&&t.jsx(Bi,{rfId:e})]})}const Hi=n.forwardRef((({position:e="top-left",children:n,className:o,style:i,...a},s)=>{const l=`${e}`.split("-");return t.jsx("div",{className:r(["react-flow__panel",o,...l]),style:i,ref:s,...a,children:n})}));function Zi({proOptions:e,position:n="bottom-right"}){return e?.hideAttribution?null:t.jsx(Hi,{position:n,className:"react-flow__attribution","data-message":"Please only hide this attribution when you are subscribed to React Flow Pro: https://pro.reactflow.dev",children:t.jsx("a",{href:"https://reactflow.dev",target:"_blank",rel:"noopener noreferrer","aria-label":"React Flow attribution",children:"React Flow"})})}function Xi(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(const[n,o]of e)if(!Object.is(o,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0}const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let o=0;o<n.length;o++)if(!Object.prototype.hasOwnProperty.call(t,n[o])||!Object.is(e[n[o]],t[n[o]]))return!1;return!0}Hi.displayName="Panel";const Yi=e=>{const t=[],n=[];for(const[,n]of e.nodeLookup)n.selected&&t.push(n.internals.userNode);for(const[,t]of e.edgeLookup)t.selected&&n.push(t);return{selectedNodes:t,selectedEdges:n}},Fi=e=>e.id;function Wi(e,t){return Xi(e.selectedNodes.map(Fi),t.selectedNodes.map(Fi))&&Xi(e.selectedEdges.map(Fi),t.selectedEdges.map(Fi))}function Ki({onSelectionChange:e}){const t=Ai(),{selectedNodes:o,selectedEdges:r}=Oi(Yi,Wi);return n.useEffect((()=>{const n={nodes:o,edges:r};e?.(n),t.getState().onSelectionChangeHandlers.forEach((e=>e(n)))}),[o,r,e]),null}const Gi=e=>!!e.onSelectionChangeHandlers;function qi({onSelectionChange:e}){const n=Oi(Gi);return e||n?t.jsx(Ki,{onSelectionChange:e}):null}const Ui=[0,0],Qi={x:0,y:0,zoom:1},Ji=["nodes","edges","defaultNodes","defaultEdges","onConnect","onConnectStart","onConnectEnd","onClickConnectStart","onClickConnectEnd","nodesDraggable","autoPanOnNodeFocus","nodesConnectable","nodesFocusable","edgesFocusable","edgesReconnectable","elevateNodesOnSelect","elevateEdgesOnSelect","minZoom","maxZoom","nodeExtent","onNodesChange","onEdgesChange","elementsSelectable","connectionMode","snapGrid","snapToGrid","translateExtent","connectOnClick","defaultEdgeOptions","fitView","fitViewOptions","onNodesDelete","onEdgesDelete","onDelete","onNodeDrag","onNodeDragStart","onNodeDragStop","onSelectionDrag","onSelectionDragStart","onSelectionDragStop","onMoveStart","onMove","onMoveEnd","noPanClassName","nodeOrigin","autoPanOnConnect","autoPanOnNodeDrag","onError","connectionRadius","isValidConnection","selectNodesOnDrag","nodeDragThreshold","connectionDragThreshold","onBeforeDelete","debug","autoPanSpeed","paneClickDistance","ariaLabelConfig","rfId"],ea=e=>({setNodes:e.setNodes,setEdges:e.setEdges,setMinZoom:e.setMinZoom,setMaxZoom:e.setMaxZoom,setTranslateExtent:e.setTranslateExtent,setNodeExtent:e.setNodeExtent,reset:e.reset,setDefaultNodesAndEdges:e.setDefaultNodesAndEdges,setPaneClickDistance:e.setPaneClickDistance}),ta={translateExtent:ro,nodeOrigin:Ui,minZoom:.5,maxZoom:2,elementsSelectable:!0,noPanClassName:"nopan",rfId:"1",paneClickDistance:0};function na(e){const{setNodes:t,setEdges:o,setMinZoom:r,setMaxZoom:i,setTranslateExtent:a,setNodeExtent:s,reset:l,setDefaultNodesAndEdges:c,setPaneClickDistance:u}=Oi(ea,Xi),d=Ai();n.useEffect((()=>(c(e.defaultNodes,e.defaultEdges),()=>{h.current=ta,l()})),[]);const h=n.useRef(ta);return n.useEffect((()=>{for(const l of Ji){const c=e[l];c!==h.current[l]&&(void 0!==e[l]&&("nodes"===l?t(c):"edges"===l?o(c):"minZoom"===l?r(c):"maxZoom"===l?i(c):"translateExtent"===l?a(c):"nodeExtent"===l?s(c):"paneClickDistance"===l?u(c):"ariaLabelConfig"===l?d.setState({ariaLabelConfig:(n=c,{...ao,...n||{}})}):"fitView"===l?d.setState({fitViewQueued:c}):"fitViewOptions"===l?d.setState({fitViewOptions:c}):d.setState({[l]:c})))}var n;h.current=e}),Ji.map((t=>e[t]))),null}function oa(){return"undefined"!=typeof window&&window.matchMedia?window.matchMedia("(prefers-color-scheme: dark)"):null}const ra="undefined"!=typeof document?document:null;function ia(e=null,t={target:ra,actInsideInputWithModifier:!0}){const[o,r]=n.useState(!1),i=n.useRef(!1),a=n.useRef(new Set([])),[s,l]=n.useMemo((()=>{if(null!==e){const t=(Array.isArray(e)?e:[e]).filter((e=>"string"==typeof e)).map((e=>e.replace("+","\n").replace("\n\n","\n+").split("\n"))),n=t.reduce(((e,t)=>e.concat(...t)),[]);return[t,n]}return[[],[]]}),[e]);return n.useEffect((()=>{const n=t?.target??ra,o=t?.actInsideInputWithModifier??!0;if(null!==e){const e=e=>{i.current=e.ctrlKey||e.metaKey||e.shiftKey||e.altKey;if((!i.current||i.current&&!o)&&rr(e))return!1;const n=sa(e.code,l);if(a.current.add(e[n]),aa(s,a.current,!1)){const n=e.composedPath?.()?.[0]||e.target,o="BUTTON"===n?.nodeName||"A"===n?.nodeName;!1===t.preventDefault||!i.current&&o||e.preventDefault(),r(!0)}},c=e=>{const t=sa(e.code,l);aa(s,a.current,!0)?(r(!1),a.current.clear()):a.current.delete(e[t]),"Meta"===e.key&&a.current.clear(),i.current=!1},u=()=>{a.current.clear(),r(!1)};return n?.addEventListener("keydown",e),n?.addEventListener("keyup",c),window.addEventListener("blur",u),window.addEventListener("contextmenu",u),()=>{n?.removeEventListener("keydown",e),n?.removeEventListener("keyup",c),window.removeEventListener("blur",u),window.removeEventListener("contextmenu",u)}}}),[e,r]),o}function aa(e,t,n){return e.filter((e=>n||e.length===t.size)).some((e=>e.every((e=>t.has(e)))))}function sa(e,t){return t.includes(e)?"code":"key"}const la=()=>{const e=Ai();return n.useMemo((()=>({zoomIn:t=>{const{panZoom:n}=e.getState();return n?n.scaleBy(1.2,{duration:t?.duration}):Promise.resolve(!1)},zoomOut:t=>{const{panZoom:n}=e.getState();return n?n.scaleBy(1/1.2,{duration:t?.duration}):Promise.resolve(!1)},zoomTo:(t,n)=>{const{panZoom:o}=e.getState();return o?o.scaleTo(t,{duration:n?.duration}):Promise.resolve(!1)},getZoom:()=>e.getState().transform[2],setViewport:async(t,n)=>{const{transform:[o,r,i],panZoom:a}=e.getState();return a?(await a.setViewport({x:t.x??o,y:t.y??r,zoom:t.zoom??i},n),Promise.resolve(!0)):Promise.resolve(!1)},getViewport:()=>{const[t,n,o]=e.getState().transform;return{x:t,y:n,zoom:o}},setCenter:async(t,n,o)=>e.getState().setCenter(t,n,o),fitBounds:async(t,n)=>{const{width:o,height:r,minZoom:i,maxZoom:a,panZoom:s}=e.getState(),l=Wo(t,o,r,i,a,n?.padding??.1);return s?(await s.setViewport(l,{duration:n?.duration,ease:n?.ease,interpolate:n?.interpolate}),Promise.resolve(!0)):Promise.resolve(!1)},screenToFlowPosition:(t,n={})=>{const{transform:o,snapGrid:r,snapToGrid:i,domNode:a}=e.getState();if(!a)return t;const{x:s,y:l}=a.getBoundingClientRect(),c={x:t.x-s,y:t.y-l},u=n.snapGrid??r,d=n.snapToGrid??i;return Xo(c,o,d,u)},flowToScreenPosition:t=>{const{transform:n,domNode:o}=e.getState();if(!o)return t;const{x:r,y:i}=o.getBoundingClientRect(),a=Yo(t,n);return{x:a.x+r,y:a.y+i}}})),[])};function ca(e,t){const n=[],o=new Map,r=[];for(const t of e)if("add"!==t.type)if("remove"===t.type||"replace"===t.type)o.set(t.id,[t]);else{const e=o.get(t.id);e?e.push(t):o.set(t.id,[t])}else r.push(t);for(const e of t){const t=o.get(e.id);if(!t){n.push(e);continue}if("remove"===t[0].type)continue;if("replace"===t[0].type){n.push({...t[0].item});continue}const r={...e};for(const e of t)ua(e,r);n.push(r)}return r.length&&r.forEach((e=>{void 0!==e.index?n.splice(e.index,0,{...e.item}):n.push({...e.item})})),n}function ua(e,t){switch(e.type){case"select":t.selected=e.selected;break;case"position":void 0!==e.position&&(t.position=e.position),void 0!==e.dragging&&(t.dragging=e.dragging);break;case"dimensions":void 0!==e.dimensions&&(t.measured??={},t.measured.width=e.dimensions.width,t.measured.height=e.dimensions.height,e.setAttributes&&(!0!==e.setAttributes&&"width"!==e.setAttributes||(t.width=e.dimensions.width),!0!==e.setAttributes&&"height"!==e.setAttributes||(t.height=e.dimensions.height))),"boolean"==typeof e.resizing&&(t.resizing=e.resizing)}}function da(e,t){return ca(e,t)}function ha(e,t){return ca(e,t)}function fa(e,t){return{id:e,type:"select",selected:t}}function ga(e,t=new Set,n=!1){const o=[];for(const[r,i]of e){const e=t.has(r);void 0===i.selected&&!e||i.selected===e||(n&&(i.selected=e),o.push(fa(i.id,e)))}return o}function pa({items:e=[],lookup:t}){const n=[],o=new Map(e.map((e=>[e.id,e])));for(const[o,r]of e.entries()){const e=t.get(r.id),i=e?.internals?.userNode??e;void 0!==i&&i!==r&&n.push({id:r.id,item:r,type:"replace"}),void 0===i&&n.push({item:r,type:"add",index:o})}for(const[e]of t){void 0===o.get(e)&&n.push({id:e,type:"remove"})}return n}function ma(e){return{id:e.id,type:"remove"}}const ya=e=>(e=>"id"in e&&"position"in e&&!("source"in e)&&!("target"in e))(e),va=e=>xo(e);function xa(e){return n.forwardRef(e)}const wa="undefined"!=typeof window?n.useLayoutEffect:n.useEffect;function ba(e){const[t,o]=n.useState(BigInt(0)),[r]=n.useState((()=>function(e){let t=[];return{get:()=>t,reset:()=>{t=[]},push:n=>{t.push(n),e()}}}((()=>o((e=>e+BigInt(1)))))));return wa((()=>{const t=r.get();t.length&&(e(t),r.reset())}),[t]),r}const Sa=n.createContext(null);function Ca({children:e}){const o=Ai(),r=ba(n.useCallback((e=>{const{nodes:t=[],setNodes:n,hasDefaultNodes:r,onNodesChange:i,nodeLookup:a,fitViewQueued:s}=o.getState();let l=t;for(const t of e)l="function"==typeof t?t(l):t;const c=pa({items:l,lookup:a});r&&n(l),c.length>0?i?.(c):s&&window.requestAnimationFrame((()=>{const{fitViewQueued:e,nodes:t,setNodes:n}=o.getState();e&&n(t)}))}),[])),i=ba(n.useCallback((e=>{const{edges:t=[],setEdges:n,hasDefaultEdges:r,onEdgesChange:i,edgeLookup:a}=o.getState();let s=t;for(const t of e)s="function"==typeof t?t(s):t;r?n(s):i&&i(pa({items:s,lookup:a}))}),[])),a=n.useMemo((()=>({nodeQueue:r,edgeQueue:i})),[]);return t.jsx(Sa.Provider,{value:a,children:e})}const Ea=e=>!!e.panZoom;function ka(){const e=la(),t=Ai(),o=function(){const e=n.useContext(Sa);if(!e)throw new Error("useBatchContext must be used within a BatchProvider");return e}(),r=Oi(Ea),i=n.useMemo((()=>{const e=e=>t.getState().nodeLookup.get(e),n=e=>{o.nodeQueue.push(e)},r=e=>{o.edgeQueue.push(e)},i=e=>{const{nodeLookup:n,nodeOrigin:o}=t.getState(),r=ya(e)?e:n.get(e.id),i=r.parentId?Qo(r.position,r.measured,r.parentId,n,o):r.position,a={...r,position:i,width:r.measured?.width??r.width,height:r.measured?.height??r.height};return Lo(a)},a=(e,t,o={replace:!1})=>{n((n=>n.map((n=>{if(n.id===e){const e="function"==typeof t?t(n):t;return o.replace&&ya(e)?e:{...n,...e}}return n}))))},s=(e,t,n={replace:!1})=>{r((o=>o.map((o=>{if(o.id===e){const e="function"==typeof t?t(o):t;return n.replace&&va(e)?e:{...o,...e}}return o}))))};return{getNodes:()=>t.getState().nodes.map((e=>({...e}))),getNode:t=>e(t)?.internals.userNode,getInternalNode:e,getEdges:()=>{const{edges:e=[]}=t.getState();return e.map((e=>({...e})))},getEdge:e=>t.getState().edgeLookup.get(e),setNodes:n,setEdges:r,addNodes:e=>{const t=Array.isArray(e)?e:[e];o.nodeQueue.push((e=>[...e,...t]))},addEdges:e=>{const t=Array.isArray(e)?e:[e];o.edgeQueue.push((e=>[...e,...t]))},toObject:()=>{const{nodes:e=[],edges:n=[],transform:o}=t.getState(),[r,i,a]=o;return{nodes:e.map((e=>({...e}))),edges:n.map((e=>({...e}))),viewport:{x:r,y:i,zoom:a}}},deleteElements:async({nodes:e=[],edges:n=[]})=>{const{nodes:o,edges:r,onNodesDelete:i,onEdgesDelete:a,triggerNodeChanges:s,triggerEdgeChanges:l,onDelete:c,onBeforeDelete:u}=t.getState(),{nodes:d,edges:h}=await async function({nodesToRemove:e=[],edgesToRemove:t=[],nodes:n,edges:o,onBeforeDelete:r}){const i=new Set(e.map((e=>e.id))),a=[];for(const e of n){if(!1===e.deletable)continue;const t=i.has(e.id),n=!t&&e.parentId&&a.find((t=>t.id===e.parentId));(t||n)&&a.push(e)}const s=new Set(t.map((e=>e.id))),l=o.filter((e=>!1!==e.deletable)),c=ko(a,l);for(const e of l)s.has(e.id)&&!c.find((t=>t.id===e.id))&&c.push(e);if(!r)return{edges:c,nodes:a};const u=await r({nodes:a,edges:c});return"boolean"==typeof u?u?{edges:c,nodes:a}:{edges:[],nodes:[]}:u}({nodesToRemove:e,edgesToRemove:n,nodes:o,edges:r,onBeforeDelete:u}),f=h.length>0,g=d.length>0;if(f){const e=h.map(ma);a?.(h),l(e)}if(g){const e=d.map(ma);i?.(d),s(e)}return(g||f)&&c?.({nodes:d,edges:h}),{deletedNodes:d,deletedEdges:h}},getIntersectingNodes:(e,n=!0,o)=>{const r=Bo(e),a=r?e:i(e),s=void 0!==o;return a?(o||t.getState().nodes).filter((o=>{const i=t.getState().nodeLookup.get(o.id);if(i&&!r&&(o.id===e.id||!i.internals.positionAbsolute))return!1;const l=Lo(s?o:i),c=Vo(l,a);return n&&c>0||c>=l.width*l.height||c>=a.width*a.height})):[]},isNodeIntersecting:(e,t,n=!0)=>{const o=Bo(e)?e:i(e);if(!o)return!1;const r=Vo(o,t);return n&&r>0||r>=o.width*o.height},updateNode:a,updateNodeData:(e,t,n={replace:!1})=>{a(e,(e=>{const o="function"==typeof t?t(e):t;return n.replace?{...e,data:o}:{...e,data:{...e.data,...o}}}),n)},updateEdge:s,updateEdgeData:(e,t,n={replace:!1})=>{s(e,(e=>{const o="function"==typeof t?t(e):t;return n.replace?{...e,data:o}:{...e,data:{...e.data,...o}}}),n)},getNodesBounds:e=>{const{nodeLookup:n,nodeOrigin:o}=t.getState();return So(e,{nodeLookup:n,nodeOrigin:o})},getHandleConnections:({type:e,id:n,nodeId:o})=>Array.from(t.getState().connectionLookup.get(`${o}-${e}${n?`-${n}`:""}`)?.values()??[]),getNodeConnections:({type:e,handleId:n,nodeId:o})=>Array.from(t.getState().connectionLookup.get(`${o}${e?n?`-${e}-${n}`:`-${e}`:""}`)?.values()??[]),fitView:async e=>{const n=t.getState().fitViewResolver??function(){let e,t;return{promise:new Promise(((n,o)=>{e=n,t=o})),resolve:e,reject:t}}();return t.setState({fitViewQueued:!0,fitViewOptions:e,fitViewResolver:n}),o.nodeQueue.push((e=>[...e])),n.promise}}}),[]);return n.useMemo((()=>({...i,...e,viewportInitialized:r})),[r])}const Ma=e=>e.selected,Na="undefined"!=typeof window?window:void 0;const _a={position:"absolute",width:"100%",height:"100%",top:0,left:0},Pa=e=>({userSelectionActive:e.userSelectionActive,lib:e.lib});function za({onPaneContextMenu:o,zoomOnScroll:r=!0,zoomOnPinch:i=!0,panOnScroll:a=!1,panOnScrollSpeed:s=.5,panOnScrollMode:l=e.PanOnScrollMode.Free,zoomOnDoubleClick:c=!0,panOnDrag:u=!0,defaultViewport:d,translateExtent:h,minZoom:f,maxZoom:g,zoomActivationKeyCode:p,preventScrolling:m=!0,children:y,noWheelClassName:v,noPanClassName:x,onViewportChange:w,isControlledViewport:b,paneClickDistance:S}){const C=Ai(),E=n.useRef(null),{userSelectionActive:k,lib:M}=Oi(Pa,Xi),N=ia(p),_=n.useRef();!function(e){const t=Ai();n.useEffect((()=>{const n=()=>{if(!e.current)return!1;const n=tr(e.current);0!==n.height&&0!==n.width||t.getState().onError?.("004",oo.error004()),t.setState({width:n.width||500,height:n.height||500})};if(e.current){n(),window.addEventListener("resize",n);const t=new ResizeObserver((()=>n()));return t.observe(e.current),()=>{window.removeEventListener("resize",n),t&&e.current&&t.unobserve(e.current)}}}),[])}(E);const P=n.useCallback((e=>{w?.({x:e[0],y:e[1],zoom:e[2]}),b||C.setState({transform:e})}),[w,b]);return n.useEffect((()=>{if(E.current){_.current=ni({domNode:E.current,minZoom:f,maxZoom:g,translateExtent:h,viewport:d,paneClickDistance:S,onDraggingChange:e=>C.setState({paneDragging:e}),onPanZoomStart:(e,t)=>{const{onViewportChangeStart:n,onMoveStart:o}=C.getState();o?.(e,t),n?.(t)},onPanZoom:(e,t)=>{const{onViewportChange:n,onMove:o}=C.getState();o?.(e,t),n?.(t)},onPanZoomEnd:(e,t)=>{const{onViewportChangeEnd:n,onMoveEnd:o}=C.getState();o?.(e,t),n?.(t)}});const{x:e,y:t,zoom:n}=_.current.getViewport();return C.setState({panZoom:_.current,transform:[e,t,n],domNode:E.current.closest(".react-flow")}),()=>{_.current?.destroy()}}}),[]),n.useEffect((()=>{_.current?.update({onPaneContextMenu:o,zoomOnScroll:r,zoomOnPinch:i,panOnScroll:a,panOnScrollSpeed:s,panOnScrollMode:l,zoomOnDoubleClick:c,panOnDrag:u,zoomActivationKeyPressed:N,preventScrolling:m,noPanClassName:x,userSelectionActive:k,noWheelClassName:v,lib:M,onTransformChange:P})}),[o,r,i,a,s,l,c,u,N,m,x,k,v,M,P]),t.jsx("div",{className:"react-flow__renderer",ref:E,style:_a,children:y})}const Oa=e=>({userSelectionActive:e.userSelectionActive,userSelectionRect:e.userSelectionRect});function Aa(){const{userSelectionActive:e,userSelectionRect:n}=Oi(Oa,Xi);return e&&n?t.jsx("div",{className:"react-flow__selection react-flow__container",style:{width:n.width,height:n.height,transform:`translate(${n.x}px, ${n.y}px)`}}):null}const Da=(e,t)=>n=>{n.target===t.current&&e?.(n)},Ia=e=>({userSelectionActive:e.userSelectionActive,elementsSelectable:e.elementsSelectable,connectionInProgress:e.connection.inProgress,dragging:e.paneDragging});function Ra({isSelecting:o,selectionKeyPressed:i,selectionMode:a=e.SelectionMode.Full,panOnDrag:s,selectionOnDrag:l,onSelectionStart:c,onSelectionEnd:u,onPaneClick:d,onPaneContextMenu:h,onPaneScroll:f,onPaneMouseEnter:g,onPaneMouseMove:p,onPaneMouseLeave:m,children:y}){const v=Ai(),{userSelectionActive:x,elementsSelectable:w,dragging:b,connectionInProgress:S}=Oi(Ia,Xi),C=w&&(o||x),E=n.useRef(null),k=n.useRef(),M=n.useRef(new Set),N=n.useRef(new Set),_=n.useRef(!1),P=n.useRef(!1),z=e=>{_.current||S?_.current=!1:(d?.(e),v.getState().resetSelectedElements(),v.setState({nodesSelectionActive:!1}))},O=f?e=>f(e):void 0,A=!0===s||Array.isArray(s)&&s.includes(0);return t.jsxs("div",{className:r(["react-flow__pane",{draggable:A,dragging:b,selection:o}]),onClick:C?void 0:Da(z,E),onContextMenu:Da((e=>{Array.isArray(s)&&s?.includes(2)?e.preventDefault():h?.(e)}),E),onWheel:Da(O,E),onPointerEnter:C?void 0:g,onPointerDown:C?e=>{const{resetSelectedElements:t,domNode:n}=v.getState();if(k.current=n?.getBoundingClientRect(),!w||!o||0!==e.button||e.target!==E.current||!k.current)return;e.target?.setPointerCapture?.(e.pointerId),P.current=!0,_.current=!1;const{x:r,y:i}=ar(e.nativeEvent,k.current);t(),v.setState({userSelectionRect:{width:0,height:0,startX:r,startY:i,x:r,y:i}}),c?.(e)}:p,onPointerMove:C?t=>{const{userSelectionRect:n,transform:o,nodeLookup:r,edgeLookup:i,connectionLookup:s,triggerNodeChanges:l,triggerEdgeChanges:c,defaultEdgeOptions:u}=v.getState();if(!k.current||!n)return;_.current=!0;const{x:d,y:h}=ar(t.nativeEvent,k.current),{startX:f,startY:g}=n,p={startX:f,startY:g,x:d<f?d:f,y:h<g?h:g,width:Math.abs(d-f),height:Math.abs(h-g)},m=M.current,y=N.current;M.current=new Set(Eo(r,p,o,a===e.SelectionMode.Partial,!0).map((e=>e.id))),N.current=new Set;const x=u?.selectable??!0;for(const e of M.current){const t=s.get(e);if(t)for(const{edgeId:e}of t.values()){const t=i.get(e);t&&(t.selectable??x)&&N.current.add(e)}}if(!Jo(m,M.current)){l(ga(r,M.current,!0))}if(!Jo(y,N.current)){c(ga(i,N.current))}v.setState({userSelectionRect:p,userSelectionActive:!0,nodesSelectionActive:!1})}:p,onPointerUp:C?e=>{if(0!==e.button||!P.current)return;e.target?.releasePointerCapture?.(e.pointerId);const{userSelectionRect:t}=v.getState();!x&&t&&e.target===E.current&&z?.(e),v.setState({userSelectionActive:!1,userSelectionRect:null,nodesSelectionActive:M.current.size>0}),u?.(e),(i||l)&&(_.current=!1),P.current=!1}:void 0,onPointerLeave:m,ref:E,style:_a,children:[y,t.jsx(Aa,{})]})}function La({id:e,store:t,unselect:n=!1,nodeRef:o}){const{addSelectedNodes:r,unselectNodesAndEdges:i,multiSelectionActive:a,nodeLookup:s,onError:l}=t.getState(),c=s.get(e);c?(t.setState({nodesSelectionActive:!1}),c.selected?(n||c.selected&&a)&&(i({nodes:[c],edges:[]}),requestAnimationFrame((()=>o?.current?.blur()))):r([e])):l?.("012",oo.error012(e))}function $a({nodeRef:e,disabled:t=!1,noDragClassName:o,handleSelector:r,nodeId:i,isSelectable:a,nodeClickDistance:s}){const l=Ai(),[c,u]=n.useState(!1),d=n.useRef();return n.useEffect((()=>{d.current=Br({getStoreItems:()=>l.getState(),onNodeMouseDown:t=>{La({id:t,store:l,nodeRef:e})},onDragStart:()=>{u(!0)},onDragStop:()=>{u(!1)}})}),[]),n.useEffect((()=>{if(t)d.current?.destroy();else if(e.current)return d.current?.update({noDragClassName:o,handleSelector:r,domNode:e.current,isSelectable:a,nodeId:i,nodeClickDistance:s}),()=>{d.current?.destroy()}}),[o,r,t,a,e,i]),c}const Ta=e=>t=>t.selected&&(t.draggable||e&&void 0===t.draggable);function Va(){const e=Ai();return n.useCallback((t=>{const{nodeExtent:n,snapToGrid:o,snapGrid:r,nodesDraggable:i,onError:a,updateNodePositions:s,nodeLookup:l,nodeOrigin:c}=e.getState(),u=new Map,d=Ta(i),h=o?r[0]:5,f=o?r[1]:5,g=t.direction.x*h*t.factor,p=t.direction.y*f*t.factor;for(const[,e]of l){if(!d(e))continue;let t={x:e.internals.positionAbsolute.x+g,y:e.internals.positionAbsolute.y+p};o&&(t=Zo(t,r));const{position:i,positionAbsolute:s}=No({nodeId:e.id,nextPosition:t,nodeLookup:l,nodeExtent:n,nodeOrigin:c,onError:a});e.position=i,e.internals.positionAbsolute=s,u.set(e.id,e)}s(u)}),[])}const Ba=n.createContext(null),ja=Ba.Provider;Ba.Consumer;const Ha=()=>n.useContext(Ba),Za=e=>({connectOnClick:e.connectOnClick,noPanClassName:e.noPanClassName,rfId:e.rfId});const Xa=n.memo(xa((function({type:n="source",position:o=e.Position.Top,isValidConnection:i,isConnectable:a=!0,isConnectableStart:s=!0,isConnectableEnd:l=!0,id:c,onConnect:u,children:d,className:h,onMouseDown:f,onTouchStart:g,...p},m){const y=c||null,v="target"===n,x=Ai(),w=Ha(),{connectOnClick:b,noPanClassName:S,rfId:C}=Oi(Za,Xi),{connectingFrom:E,connectingTo:k,clickConnecting:M,isPossibleEndHandle:N,connectionInProcess:_,clickConnectionInProcess:P,valid:z}=Oi(((t,n,o)=>r=>{const{connectionClickStartHandle:i,connectionMode:a,connection:s}=r,{fromHandle:l,toHandle:c,isValid:u}=s,d=c?.nodeId===t&&c?.id===n&&c?.type===o;return{connectingFrom:l?.nodeId===t&&l?.id===n&&l?.type===o,connectingTo:d,clickConnecting:i?.nodeId===t&&i?.id===n&&i?.type===o,isPossibleEndHandle:a===e.ConnectionMode.Strict?l?.type!==o:t!==l?.nodeId||n!==l?.id,connectionInProcess:!!l,clickConnectionInProcess:!!i,valid:d&&u}})(w,y,n),Xi);w||x.getState().onError?.("010",oo.error010());const O=e=>{const{defaultEdgeOptions:t,onConnect:n,hasDefaultEdges:o}=x.getState(),r={...t,...e};if(o){const{edges:e,setEdges:t}=x.getState();t(pr(r,e))}n?.(r),u?.(r)},A=e=>{if(!w)return;const t=ir(e.nativeEvent);if(s&&(t&&0===e.button||!t)){const t=x.getState();Wr.onPointerDown(e.nativeEvent,{handleDomNode:e.currentTarget,autoPanOnConnect:t.autoPanOnConnect,connectionMode:t.connectionMode,connectionRadius:t.connectionRadius,domNode:t.domNode,nodeLookup:t.nodeLookup,lib:t.lib,isTarget:v,handleId:y,nodeId:w,flowId:t.rfId,panBy:t.panBy,cancelConnection:t.cancelConnection,onConnectStart:t.onConnectStart,onConnectEnd:t.onConnectEnd,updateConnection:t.updateConnection,onConnect:O,isValidConnection:i||t.isValidConnection,getTransform:()=>x.getState().transform,getFromHandle:()=>x.getState().connection.fromHandle,autoPanSpeed:t.autoPanSpeed,dragThreshold:t.connectionDragThreshold})}t?f?.(e):g?.(e)};return t.jsx("div",{"data-handleid":y,"data-nodeid":w,"data-handlepos":o,"data-id":`${C}-${w}-${y}-${n}`,className:r(["react-flow__handle",`react-flow__handle-${o}`,"nodrag",S,h,{source:!v,target:v,connectable:a,connectablestart:s,connectableend:l,clickconnecting:M,connectingfrom:E,connectingto:k,valid:z,connectionindicator:a&&(!_||N)&&(_||P?l:s)}]),onMouseDown:A,onTouchStart:A,onClick:b?e=>{const{onClickConnectStart:t,onClickConnectEnd:o,connectionClickStartHandle:r,connectionMode:a,isValidConnection:l,lib:c,rfId:u,nodeLookup:d,connection:h}=x.getState();if(!w||!r&&!s)return;if(!r)return t?.(e.nativeEvent,{nodeId:w,handleId:y,handleType:n}),void x.setState({connectionClickStartHandle:{nodeId:w,type:n,id:y}});const f=nr(e.target),g=i||l,{connection:p,isValid:m}=Wr.isValid(e.nativeEvent,{handle:{nodeId:w,id:y,type:n},connectionMode:a,fromNodeId:r.nodeId,fromHandleId:r.id||null,fromType:r.type,isValidConnection:g,flowId:u,doc:f,lib:c,nodeLookup:d});m&&p&&O(p);const v=structuredClone(h);delete v.inProgress,v.toPosition=v.toHandle?v.toHandle.position:null,o?.(e,v),x.setState({connectionClickStartHandle:null})}:void 0,ref:m,...p,children:d})})));const Ya={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}},Fa={input:function({data:n,isConnectable:o,sourcePosition:r=e.Position.Bottom}){return t.jsxs(t.Fragment,{children:[n?.label,t.jsx(Xa,{type:"source",position:r,isConnectable:o})]})},default:function({data:n,isConnectable:o,targetPosition:r=e.Position.Top,sourcePosition:i=e.Position.Bottom}){return t.jsxs(t.Fragment,{children:[t.jsx(Xa,{type:"target",position:r,isConnectable:o}),n?.label,t.jsx(Xa,{type:"source",position:i,isConnectable:o})]})},output:function({data:n,isConnectable:o,targetPosition:r=e.Position.Top}){return t.jsxs(t.Fragment,{children:[t.jsx(Xa,{type:"target",position:r,isConnectable:o}),n?.label]})},group:function(){return null}};const Wa=e=>{const{width:t,height:n,x:o,y:r}=Co(e.nodeLookup,{filter:e=>!!e.selected});return{width:jo(t)?t:null,height:jo(n)?n:null,userSelectionActive:e.userSelectionActive,transformString:`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]}) translate(${o}px,${r}px)`}};function Ka({onSelectionContextMenu:e,noPanClassName:o,disableKeyboardA11y:i}){const a=Ai(),{width:s,height:l,transformString:c,userSelectionActive:u}=Oi(Wa,Xi),d=Va(),h=n.useRef(null);if(n.useEffect((()=>{i||h.current?.focus({preventScroll:!0})}),[i]),$a({nodeRef:h}),u||!s||!l)return null;const f=e?t=>{const n=a.getState().nodes.filter((e=>e.selected));e(t,n)}:void 0;return t.jsx("div",{className:r(["react-flow__nodesselection","react-flow__container",o]),style:{transform:c},children:t.jsx("div",{ref:h,className:"react-flow__nodesselection-rect",onContextMenu:f,tabIndex:i?void 0:-1,onKeyDown:i?void 0:e=>{Object.prototype.hasOwnProperty.call(Ya,e.key)&&(e.preventDefault(),d({direction:Ya[e.key],factor:e.shiftKey?4:1}))},style:{width:s,height:l}})})}const Ga="undefined"!=typeof window?window:void 0,qa=e=>({nodesSelectionActive:e.nodesSelectionActive,userSelectionActive:e.userSelectionActive});function Ua({children:e,onPaneClick:o,onPaneMouseEnter:r,onPaneMouseMove:i,onPaneMouseLeave:a,onPaneContextMenu:s,onPaneScroll:l,paneClickDistance:c,deleteKeyCode:u,selectionKeyCode:d,selectionOnDrag:h,selectionMode:f,onSelectionStart:g,onSelectionEnd:p,multiSelectionKeyCode:m,panActivationKeyCode:y,zoomActivationKeyCode:v,elementsSelectable:x,zoomOnScroll:w,zoomOnPinch:b,panOnScroll:S,panOnScrollSpeed:C,panOnScrollMode:E,zoomOnDoubleClick:k,panOnDrag:M,defaultViewport:N,translateExtent:_,minZoom:P,maxZoom:z,preventScrolling:O,onSelectionContextMenu:A,noWheelClassName:D,noPanClassName:I,disableKeyboardA11y:R,onViewportChange:L,isControlledViewport:$}){const{nodesSelectionActive:T,userSelectionActive:V}=Oi(qa),B=ia(d,{target:Ga}),j=ia(y,{target:Ga}),H=j||M,Z=j||S,X=h&&!0!==H,Y=B||V||X;return function({deleteKeyCode:e,multiSelectionKeyCode:t}){const o=Ai(),{deleteElements:r}=ka(),i=ia(e,{actInsideInputWithModifier:!1}),a=ia(t,{target:Na});n.useEffect((()=>{if(i){const{edges:e,nodes:t}=o.getState();r({nodes:t.filter(Ma),edges:e.filter(Ma)}),o.setState({nodesSelectionActive:!1})}}),[i]),n.useEffect((()=>{o.setState({multiSelectionActive:a})}),[a])}({deleteKeyCode:u,multiSelectionKeyCode:m}),t.jsx(za,{onPaneContextMenu:s,elementsSelectable:x,zoomOnScroll:w,zoomOnPinch:b,panOnScroll:Z,panOnScrollSpeed:C,panOnScrollMode:E,zoomOnDoubleClick:k,panOnDrag:!B&&H,defaultViewport:N,translateExtent:_,minZoom:P,maxZoom:z,zoomActivationKeyCode:v,preventScrolling:O,noWheelClassName:D,noPanClassName:I,onViewportChange:L,isControlledViewport:$,paneClickDistance:c,children:t.jsxs(Ra,{onSelectionStart:g,onSelectionEnd:p,onPaneClick:o,onPaneMouseEnter:r,onPaneMouseMove:i,onPaneMouseLeave:a,onPaneContextMenu:s,onPaneScroll:l,panOnDrag:H,isSelecting:!!Y,selectionMode:f,selectionKeyPressed:B,selectionOnDrag:X,children:[e,T&&t.jsx(Ka,{onSelectionContextMenu:A,noPanClassName:I,disableKeyboardA11y:R})]})})}Ua.displayName="FlowRenderer";const Qa=n.memo(Ua),Ja=e=>t=>e?Eo(t.nodeLookup,{x:0,y:0,width:t.width,height:t.height},t.transform,!0).map((e=>e.id)):Array.from(t.nodeLookup.keys());const es=e=>e.updateNodeInternals;function ts({id:e,onClick:o,onMouseEnter:i,onMouseMove:a,onMouseLeave:s,onContextMenu:l,onDoubleClick:c,nodesDraggable:u,elementsSelectable:d,nodesConnectable:h,nodesFocusable:f,resizeObserver:g,noDragClassName:p,noPanClassName:m,disableKeyboardA11y:y,rfId:v,nodeTypes:x,nodeClickDistance:w,onError:b}){const{node:S,internals:C,isParent:E}=Oi((t=>{const n=t.nodeLookup.get(e),o=t.parentLookup.has(e);return{node:n,internals:n.internals,isParent:o}}),Xi);let k=S.type||"default",M=x?.[k]||Fa[k];void 0===M&&(b?.("003",oo.error003(k)),k="default",M=x?.default||Fa.default);const N=!!(S.draggable||u&&void 0===S.draggable),_=!!(S.selectable||d&&void 0===S.selectable),P=!!(S.connectable||h&&void 0===S.connectable),z=!!(S.focusable||f&&void 0===S.focusable),O=Ai(),A=Uo(S),D=function({node:e,nodeType:t,hasDimensions:o,resizeObserver:r}){const i=Ai(),a=n.useRef(null),s=n.useRef(null),l=n.useRef(e.sourcePosition),c=n.useRef(e.targetPosition),u=n.useRef(t),d=o&&!!e.internals.handleBounds;return n.useEffect((()=>{!a.current||e.hidden||d&&s.current===a.current||(s.current&&r?.unobserve(s.current),r?.observe(a.current),s.current=a.current)}),[d,e.hidden]),n.useEffect((()=>()=>{s.current&&(r?.unobserve(s.current),s.current=null)}),[]),n.useEffect((()=>{if(a.current){const n=u.current!==t,o=l.current!==e.sourcePosition,r=c.current!==e.targetPosition;(n||o||r)&&(u.current=t,l.current=e.sourcePosition,c.current=e.targetPosition,i.getState().updateNodeInternals(new Map([[e.id,{id:e.id,nodeElement:a.current,force:!0}]])))}}),[e.id,t,e.sourcePosition,e.targetPosition]),a}({node:S,nodeType:k,hasDimensions:A,resizeObserver:g}),I=$a({nodeRef:D,disabled:S.hidden||!N,noDragClassName:p,handleSelector:S.dragHandle,nodeId:e,isSelectable:_,nodeClickDistance:w}),R=Va();if(S.hidden)return null;const L=qo(S),$=function(e){return void 0===e.internals.handleBounds?{width:e.width??e.initialWidth??e.style?.width,height:e.height??e.initialHeight??e.style?.height}:{width:e.width??e.style?.width,height:e.height??e.style?.height}}(S),T=_||N||o||i||a||s,V=i?e=>i(e,{...C.userNode}):void 0,B=a?e=>a(e,{...C.userNode}):void 0,j=s?e=>s(e,{...C.userNode}):void 0,H=l?e=>l(e,{...C.userNode}):void 0,Z=c?e=>c(e,{...C.userNode}):void 0;return t.jsx("div",{className:r(["react-flow__node",`react-flow__node-${k}`,{[m]:N},S.className,{selected:S.selected,selectable:_,parent:E,draggable:N,dragging:I}]),ref:D,style:{zIndex:C.z,transform:`translate(${C.positionAbsolute.x}px,${C.positionAbsolute.y}px)`,pointerEvents:T?"all":"none",visibility:A?"visible":"hidden",...S.style,...$},"data-id":e,"data-testid":`rf__node-${e}`,onMouseEnter:V,onMouseMove:B,onMouseLeave:j,onContextMenu:H,onClick:t=>{const{selectNodesOnDrag:n,nodeDragThreshold:r}=O.getState();_&&(!n||!N||r>0)&&La({id:e,store:O,nodeRef:D}),o&&o(t,{...C.userNode})},onDoubleClick:Z,onKeyDown:z?t=>{if(!rr(t.nativeEvent)&&!y)if(io.includes(t.key)&&_){const n="Escape"===t.key;La({id:e,store:O,unselect:n,nodeRef:D})}else if(N&&S.selected&&Object.prototype.hasOwnProperty.call(Ya,t.key)){t.preventDefault();const{ariaLabelConfig:e}=O.getState();O.setState({ariaLiveMessage:e["node.a11yDescription.ariaLiveMessage"]({direction:t.key.replace("Arrow","").toLowerCase(),x:~~C.positionAbsolute.x,y:~~C.positionAbsolute.y})}),R({direction:Ya[t.key],factor:t.shiftKey?4:1})}}:void 0,tabIndex:z?0:void 0,onFocus:z?()=>{if(y||!D.current?.matches(":focus-visible"))return;const{transform:t,width:n,height:o,autoPanOnNodeFocus:r,setCenter:i}=O.getState();if(!r)return;Eo(new Map([[e,S]]),{x:0,y:0,width:n,height:o},t,!0).length>0||i(S.position.x+L.width/2,S.position.y+L.height/2,{zoom:t[2]})}:void 0,role:S.ariaRole??(z?"group":void 0),"aria-roledescription":"node","aria-describedby":y?void 0:`${Ri}-${v}`,"aria-label":S.ariaLabel,...S.domAttributes,children:t.jsx(ja,{value:e,children:t.jsx(M,{id:e,data:S.data,type:k,positionAbsoluteX:C.positionAbsolute.x,positionAbsoluteY:C.positionAbsolute.y,selected:S.selected??!1,selectable:_,draggable:N,deletable:S.deletable??!0,isConnectable:P,sourcePosition:S.sourcePosition,targetPosition:S.targetPosition,dragging:I,dragHandle:S.dragHandle,zIndex:C.z,parentId:S.parentId,...L})})})}const ns=e=>({nodesDraggable:e.nodesDraggable,nodesConnectable:e.nodesConnectable,nodesFocusable:e.nodesFocusable,elementsSelectable:e.elementsSelectable,onError:e.onError});function os(e){const{nodesDraggable:o,nodesConnectable:r,nodesFocusable:i,elementsSelectable:a,onError:s}=Oi(ns,Xi),l=(c=e.onlyRenderVisibleElements,Oi(n.useCallback(Ja(c),[c]),Xi));var c;const u=function(){const e=Oi(es),[t]=n.useState((()=>"undefined"==typeof ResizeObserver?null:new ResizeObserver((t=>{const n=new Map;t.forEach((e=>{const t=e.target.getAttribute("data-id");n.set(t,{id:t,nodeElement:e.target,force:!0})})),e(n)}))));return n.useEffect((()=>()=>{t?.disconnect()}),[t]),t}();return t.jsx("div",{className:"react-flow__nodes",style:_a,children:l.map((n=>t.jsx(ts,{id:n,nodeTypes:e.nodeTypes,nodeExtent:e.nodeExtent,onClick:e.onNodeClick,onMouseEnter:e.onNodeMouseEnter,onMouseMove:e.onNodeMouseMove,onMouseLeave:e.onNodeMouseLeave,onContextMenu:e.onNodeContextMenu,onDoubleClick:e.onNodeDoubleClick,noDragClassName:e.noDragClassName,noPanClassName:e.noPanClassName,rfId:e.rfId,disableKeyboardA11y:e.disableKeyboardA11y,resizeObserver:u,nodesDraggable:o,nodesConnectable:r,nodesFocusable:i,elementsSelectable:a,nodeClickDistance:e.nodeClickDistance,onError:s},n)))})}os.displayName="NodeRenderer";const rs=n.memo(os);const is={[e.MarkerType.Arrow]:({color:e="none",strokeWidth:n=1})=>{const o={strokeWidth:n,...e&&{stroke:e}};return t.jsx("polyline",{className:"arrow",style:o,strokeLinecap:"round",fill:"none",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4"})},[e.MarkerType.ArrowClosed]:({color:e="none",strokeWidth:n=1})=>{const o={strokeWidth:n,...e&&{stroke:e,fill:e}};return t.jsx("polyline",{className:"arrowclosed",style:o,strokeLinecap:"round",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4 -5,-4"})}};const as=({id:e,type:o,color:r,width:i=12.5,height:a=12.5,markerUnits:s="strokeWidth",strokeWidth:l,orient:c="auto-start-reverse"})=>{const u=function(e){const t=Ai();return n.useMemo((()=>Object.prototype.hasOwnProperty.call(is,e)?is[e]:(t.getState().onError?.("009",oo.error009(e)),null)),[e])}(o);return u?t.jsx("marker",{className:"react-flow__arrowhead",id:e,markerWidth:`${i}`,markerHeight:`${a}`,viewBox:"-10 -10 20 20",markerUnits:s,orient:c,refX:"0",refY:"0",children:t.jsx(u,{color:r,strokeWidth:l})}):null},ss=({defaultColor:e,rfId:o})=>{const r=Oi((e=>e.edges)),i=Oi((e=>e.defaultEdgeOptions)),a=n.useMemo((()=>{const t=function(e,{id:t,defaultColor:n,defaultMarkerStart:o,defaultMarkerEnd:r}){const i=new Set;return e.reduce(((e,a)=>([a.markerStart||o,a.markerEnd||r].forEach((o=>{if(o&&"object"==typeof o){const r=kr(o,t);i.has(r)||(e.push({id:r,color:o.color||n,...o}),i.add(r))}})),e)),[]).sort(((e,t)=>e.id.localeCompare(t.id)))}(r,{id:o,defaultColor:e,defaultMarkerStart:i?.markerStart,defaultMarkerEnd:i?.markerEnd});return t}),[r,i,o,e]);return a.length?t.jsx("svg",{className:"react-flow__marker","aria-hidden":"true",children:t.jsx("defs",{children:a.map((e=>t.jsx(as,{id:e.id,type:e.type,color:e.color,width:e.width,height:e.height,markerUnits:e.markerUnits,strokeWidth:e.strokeWidth,orient:e.orient},e.id)))})}):null};ss.displayName="MarkerDefinitions";var ls=n.memo(ss);function cs({x:e,y:o,label:i,labelStyle:a,labelShowBg:s=!0,labelBgStyle:l,labelBgPadding:c=[2,4],labelBgBorderRadius:u=2,children:d,className:h,...f}){const[g,p]=n.useState({x:1,y:0,width:0,height:0}),m=r(["react-flow__edge-textwrapper",h]),y=n.useRef(null);return n.useEffect((()=>{if(y.current){const e=y.current.getBBox();p({x:e.x,y:e.y,width:e.width,height:e.height})}}),[i]),i?t.jsxs("g",{transform:`translate(${e-g.width/2} ${o-g.height/2})`,className:m,visibility:g.width?"visible":"hidden",...f,children:[s&&t.jsx("rect",{width:g.width+2*c[0],x:-c[0],y:-c[1],height:g.height+2*c[1],className:"react-flow__edge-textbg",style:l,rx:u,ry:u}),t.jsx("text",{className:"react-flow__edge-text",y:g.height/2,dy:"0.3em",ref:y,style:a,children:i}),d]}):null}cs.displayName="EdgeText";const us=n.memo(cs);function ds({path:e,labelX:n,labelY:o,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,interactionWidth:d=20,...h}){return t.jsxs(t.Fragment,{children:[t.jsx("path",{...h,d:e,fill:"none",className:r(["react-flow__edge-path",h.className])}),d?t.jsx("path",{d:e,fill:"none",strokeOpacity:0,strokeWidth:d,className:"react-flow__edge-interaction"}):null,i&&jo(n)&&jo(o)?t.jsx(us,{x:n,y:o,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u}):null]})}function hs({pos:t,x1:n,y1:o,x2:r,y2:i}){return t===e.Position.Left||t===e.Position.Right?[.5*(n+r),o]:[n,.5*(o+i)]}function fs({sourceX:t,sourceY:n,sourcePosition:o=e.Position.Bottom,targetX:r,targetY:i,targetPosition:a=e.Position.Top}){const[s,l]=hs({pos:o,x1:t,y1:n,x2:r,y2:i}),[c,u]=hs({pos:a,x1:r,y1:i,x2:t,y2:n}),[d,h,f,g]=lr({sourceX:t,sourceY:n,targetX:r,targetY:i,sourceControlX:s,sourceControlY:l,targetControlX:c,targetControlY:u});return[`M${t},${n} C${s},${l} ${c},${u} ${r},${i}`,d,h,f,g]}function gs(e){return n.memo((({id:n,sourceX:o,sourceY:r,targetX:i,targetY:a,sourcePosition:s,targetPosition:l,label:c,labelStyle:u,labelShowBg:d,labelBgStyle:h,labelBgPadding:f,labelBgBorderRadius:g,style:p,markerEnd:m,markerStart:y,interactionWidth:v})=>{const[x,w,b]=fs({sourceX:o,sourceY:r,sourcePosition:s,targetX:i,targetY:a,targetPosition:l}),S=e.isInternal?void 0:n;return t.jsx(ds,{id:S,path:x,labelX:w,labelY:b,label:c,labelStyle:u,labelShowBg:d,labelBgStyle:h,labelBgPadding:f,labelBgBorderRadius:g,style:p,markerEnd:m,markerStart:y,interactionWidth:v})}))}const ps=gs({isInternal:!1}),ms=gs({isInternal:!0});function ys(o){return n.memo((({id:n,sourceX:r,sourceY:i,targetX:a,targetY:s,label:l,labelStyle:c,labelShowBg:u,labelBgStyle:d,labelBgPadding:h,labelBgBorderRadius:f,style:g,sourcePosition:p=e.Position.Bottom,targetPosition:m=e.Position.Top,markerEnd:y,markerStart:v,pathOptions:x,interactionWidth:w})=>{const[b,S,C]=wr({sourceX:r,sourceY:i,sourcePosition:p,targetX:a,targetY:s,targetPosition:m,borderRadius:x?.borderRadius,offset:x?.offset,stepPosition:x?.stepPosition}),E=o.isInternal?void 0:n;return t.jsx(ds,{id:E,path:b,labelX:S,labelY:C,label:l,labelStyle:c,labelShowBg:u,labelBgStyle:d,labelBgPadding:h,labelBgBorderRadius:f,style:g,markerEnd:y,markerStart:v,interactionWidth:w})}))}ps.displayName="SimpleBezierEdge",ms.displayName="SimpleBezierEdgeInternal";const vs=ys({isInternal:!1}),xs=ys({isInternal:!0});function ws(e){return n.memo((({id:o,...r})=>{const i=e.isInternal?void 0:o;return t.jsx(vs,{...r,id:i,pathOptions:n.useMemo((()=>({borderRadius:0,offset:r.pathOptions?.offset})),[r.pathOptions?.offset])})}))}vs.displayName="SmoothStepEdge",xs.displayName="SmoothStepEdgeInternal";const bs=ws({isInternal:!1}),Ss=ws({isInternal:!0});function Cs(e){return n.memo((({id:n,sourceX:o,sourceY:r,targetX:i,targetY:a,label:s,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:d,labelBgBorderRadius:h,style:f,markerEnd:g,markerStart:p,interactionWidth:m})=>{const[y,v,x]=mr({sourceX:o,sourceY:r,targetX:i,targetY:a}),w=e.isInternal?void 0:n;return t.jsx(ds,{id:w,path:y,labelX:v,labelY:x,label:s,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:d,labelBgBorderRadius:h,style:f,markerEnd:g,markerStart:p,interactionWidth:m})}))}bs.displayName="StepEdge",Ss.displayName="StepEdgeInternal";const Es=Cs({isInternal:!1}),ks=Cs({isInternal:!0});function Ms(o){return n.memo((({id:n,sourceX:r,sourceY:i,targetX:a,targetY:s,sourcePosition:l=e.Position.Bottom,targetPosition:c=e.Position.Top,label:u,labelStyle:d,labelShowBg:h,labelBgStyle:f,labelBgPadding:g,labelBgBorderRadius:p,style:m,markerEnd:y,markerStart:v,pathOptions:x,interactionWidth:w})=>{const[b,S,C]=dr({sourceX:r,sourceY:i,sourcePosition:l,targetX:a,targetY:s,targetPosition:c,curvature:x?.curvature}),E=o.isInternal?void 0:n;return t.jsx(ds,{id:E,path:b,labelX:S,labelY:C,label:u,labelStyle:d,labelShowBg:h,labelBgStyle:f,labelBgPadding:g,labelBgBorderRadius:p,style:m,markerEnd:y,markerStart:v,interactionWidth:w})}))}Es.displayName="StraightEdge",ks.displayName="StraightEdgeInternal";const Ns=Ms({isInternal:!1}),_s=Ms({isInternal:!0});Ns.displayName="BezierEdge",_s.displayName="BezierEdgeInternal";const Ps={default:_s,straight:ks,step:Ss,smoothstep:xs,simplebezier:ms},zs={sourceX:null,sourceY:null,targetX:null,targetY:null,sourcePosition:null,targetPosition:null},Os=(t,n,o)=>o===e.Position.Left?t-n:o===e.Position.Right?t+n:t,As=(t,n,o)=>o===e.Position.Top?t-n:o===e.Position.Bottom?t+n:t,Ds="react-flow__edgeupdater";function Is({position:e,centerX:n,centerY:o,radius:i=10,onMouseDown:a,onMouseEnter:s,onMouseOut:l,type:c}){return t.jsx("circle",{onMouseDown:a,onMouseEnter:s,onMouseOut:l,className:r([Ds,`${Ds}-${c}`]),cx:Os(n,i,e),cy:As(o,i,e),r:i,stroke:"transparent",fill:"transparent"})}function Rs({isReconnectable:e,reconnectRadius:n,edge:o,sourceX:r,sourceY:i,targetX:a,targetY:s,sourcePosition:l,targetPosition:c,onReconnect:u,onReconnectStart:d,onReconnectEnd:h,setReconnecting:f,setUpdateHover:g}){const p=Ai(),m=(e,t)=>{if(0!==e.button)return;const{autoPanOnConnect:n,domNode:r,isValidConnection:i,connectionMode:a,connectionRadius:s,lib:l,onConnectStart:c,onConnectEnd:g,cancelConnection:m,nodeLookup:y,rfId:v,panBy:x,updateConnection:w}=p.getState(),b="target"===t.type;Wr.onPointerDown(e.nativeEvent,{autoPanOnConnect:n,connectionMode:a,connectionRadius:s,domNode:r,handleId:t.id,nodeId:t.nodeId,nodeLookup:y,isTarget:b,edgeUpdaterType:t.type,lib:l,flowId:v,cancelConnection:m,panBy:x,isValidConnection:i,onConnect:e=>u?.(o,e),onConnectStart:(n,r)=>{f(!0),d?.(e,o,t.type),c?.(n,r)},onConnectEnd:g,onReconnectEnd:(e,n)=>{f(!1),h?.(e,o,t.type,n)},updateConnection:w,getTransform:()=>p.getState().transform,getFromHandle:()=>p.getState().connection.fromHandle,dragThreshold:p.getState().connectionDragThreshold,handleDomNode:e.currentTarget})},y=()=>g(!0),v=()=>g(!1);return t.jsxs(t.Fragment,{children:[(!0===e||"source"===e)&&t.jsx(Is,{position:l,centerX:r,centerY:i,radius:n,onMouseDown:e=>m(e,{nodeId:o.target,id:o.targetHandle??null,type:"target"}),onMouseEnter:y,onMouseOut:v,type:"source"}),(!0===e||"target"===e)&&t.jsx(Is,{position:c,centerX:a,centerY:s,radius:n,onMouseDown:e=>m(e,{nodeId:o.source,id:o.sourceHandle??null,type:"source"}),onMouseEnter:y,onMouseOut:v,type:"target"})]})}function Ls({id:o,edgesFocusable:i,edgesReconnectable:a,elementsSelectable:s,onClick:l,onDoubleClick:c,onContextMenu:u,onMouseEnter:d,onMouseMove:h,onMouseLeave:f,reconnectRadius:g,onReconnect:p,onReconnectStart:m,onReconnectEnd:y,rfId:v,edgeTypes:x,noPanClassName:w,onError:b,disableKeyboardA11y:S}){let C=Oi((e=>e.edgeLookup.get(o)));const E=Oi((e=>e.defaultEdgeOptions));C=E?{...E,...C}:C;let k=C.type||"default",M=x?.[k]||Ps[k];void 0===M&&(b?.("011",oo.error011(k)),k="default",M=x?.default||Ps.default);const N=!!(C.focusable||i&&void 0===C.focusable),_=void 0!==p&&(C.reconnectable||a&&void 0===C.reconnectable),P=!!(C.selectable||s&&void 0===C.selectable),z=n.useRef(null),[O,A]=n.useState(!1),[D,I]=n.useState(!1),R=Ai(),{zIndex:L,sourceX:$,sourceY:T,targetX:V,targetY:B,sourcePosition:j,targetPosition:H}=Oi(n.useCallback((t=>{const n=t.nodeLookup.get(C.source),r=t.nodeLookup.get(C.target);if(!n||!r)return{zIndex:C.zIndex,...zs};const i=function(t){const{sourceNode:n,targetNode:o}=t;if(!br(n)||!br(o))return null;const r=n.internals.handleBounds||Sr(n.handles),i=o.internals.handleBounds||Sr(o.handles),a=Er(r?.source??[],t.sourceHandle),s=Er(t.connectionMode===e.ConnectionMode.Strict?i?.target??[]:(i?.target??[]).concat(i?.source??[]),t.targetHandle);if(!a||!s)return t.onError?.("008",oo.error008(a?"target":"source",{id:t.id,sourceHandle:t.sourceHandle,targetHandle:t.targetHandle})),null;const l=a?.position||e.Position.Bottom,c=s?.position||e.Position.Top,u=Cr(n,a,l),d=Cr(o,s,c);return{sourceX:u.x,sourceY:u.y,targetX:d.x,targetY:d.y,sourcePosition:l,targetPosition:c}}({id:o,sourceNode:n,targetNode:r,sourceHandle:C.sourceHandle||null,targetHandle:C.targetHandle||null,connectionMode:t.connectionMode,onError:b}),a=function({sourceNode:e,targetNode:t,selected:n=!1,zIndex:o,elevateOnSelect:r=!1}){return void 0!==o?o:(r&&n?1e3:0)+Math.max(e.parentId?e.internals.z:0,t.parentId?t.internals.z:0)}({selected:C.selected,zIndex:C.zIndex,sourceNode:n,targetNode:r,elevateOnSelect:t.elevateEdgesOnSelect});return{zIndex:a,...i||zs}}),[C.source,C.target,C.sourceHandle,C.targetHandle,C.selected,C.zIndex]),Xi),Z=n.useMemo((()=>C.markerStart?`url('#${kr(C.markerStart,v)}')`:void 0),[C.markerStart,v]),X=n.useMemo((()=>C.markerEnd?`url('#${kr(C.markerEnd,v)}')`:void 0),[C.markerEnd,v]);if(C.hidden||null===$||null===T||null===V||null===B)return null;const Y=c?e=>{c(e,{...C})}:void 0,F=u?e=>{u(e,{...C})}:void 0,W=d?e=>{d(e,{...C})}:void 0,K=h?e=>{h(e,{...C})}:void 0,G=f?e=>{f(e,{...C})}:void 0;return t.jsx("svg",{style:{zIndex:L},children:t.jsxs("g",{className:r(["react-flow__edge",`react-flow__edge-${k}`,C.className,w,{selected:C.selected,animated:C.animated,inactive:!P&&!l,updating:O,selectable:P}]),onClick:e=>{const{addSelectedEdges:t,unselectNodesAndEdges:n,multiSelectionActive:r}=R.getState();P&&(R.setState({nodesSelectionActive:!1}),C.selected&&r?(n({nodes:[],edges:[C]}),z.current?.blur()):t([o])),l&&l(e,C)},onDoubleClick:Y,onContextMenu:F,onMouseEnter:W,onMouseMove:K,onMouseLeave:G,onKeyDown:N?e=>{if(!S&&io.includes(e.key)&&P){const{unselectNodesAndEdges:t,addSelectedEdges:n}=R.getState();"Escape"===e.key?(z.current?.blur(),t({edges:[C]})):n([o])}}:void 0,tabIndex:N?0:void 0,role:C.ariaRole??(N?"group":"img"),"aria-roledescription":"edge","data-id":o,"data-testid":`rf__edge-${o}`,"aria-label":null===C.ariaLabel?void 0:C.ariaLabel||`Edge from ${C.source} to ${C.target}`,"aria-describedby":N?`${Li}-${v}`:void 0,ref:z,...C.domAttributes,children:[!D&&t.jsx(M,{id:o,source:C.source,target:C.target,type:C.type,selected:C.selected,animated:C.animated,selectable:P,deletable:C.deletable??!0,label:C.label,labelStyle:C.labelStyle,labelShowBg:C.labelShowBg,labelBgStyle:C.labelBgStyle,labelBgPadding:C.labelBgPadding,labelBgBorderRadius:C.labelBgBorderRadius,sourceX:$,sourceY:T,targetX:V,targetY:B,sourcePosition:j,targetPosition:H,data:C.data,style:C.style,sourceHandleId:C.sourceHandle,targetHandleId:C.targetHandle,markerStart:Z,markerEnd:X,pathOptions:"pathOptions"in C?C.pathOptions:void 0,interactionWidth:C.interactionWidth}),_&&t.jsx(Rs,{edge:C,isReconnectable:_,reconnectRadius:g,onReconnect:p,onReconnectStart:m,onReconnectEnd:y,sourceX:$,sourceY:T,targetX:V,targetY:B,sourcePosition:j,targetPosition:H,setUpdateHover:A,setReconnecting:I})]})})}const $s=e=>({edgesFocusable:e.edgesFocusable,edgesReconnectable:e.edgesReconnectable,elementsSelectable:e.elementsSelectable,connectionMode:e.connectionMode,onError:e.onError});function Ts({defaultMarkerColor:e,onlyRenderVisibleElements:o,rfId:r,edgeTypes:i,noPanClassName:a,onReconnect:s,onEdgeContextMenu:l,onEdgeMouseEnter:c,onEdgeMouseMove:u,onEdgeMouseLeave:d,onEdgeClick:h,reconnectRadius:f,onEdgeDoubleClick:g,onReconnectStart:p,onReconnectEnd:m,disableKeyboardA11y:y}){const{edgesFocusable:v,edgesReconnectable:x,elementsSelectable:w,onError:b}=Oi($s,Xi),S=(C=o,Oi(n.useCallback((e=>{if(!C)return e.edges.map((e=>e.id));const t=[];if(e.width&&e.height)for(const n of e.edges){const o=e.nodeLookup.get(n.source),r=e.nodeLookup.get(n.target);o&&r&&fr({sourceNode:o,targetNode:r,width:e.width,height:e.height,transform:e.transform})&&t.push(n.id)}return t}),[C]),Xi));var C;return t.jsxs("div",{className:"react-flow__edges",children:[t.jsx(ls,{defaultColor:e,rfId:r}),S.map((e=>t.jsx(Ls,{id:e,edgesFocusable:v,edgesReconnectable:x,elementsSelectable:w,noPanClassName:a,onReconnect:s,onContextMenu:l,onMouseEnter:c,onMouseMove:u,onMouseLeave:d,onClick:h,reconnectRadius:f,onDoubleClick:g,onReconnectStart:p,onReconnectEnd:m,rfId:r,onError:b,edgeTypes:i,disableKeyboardA11y:y},e)))]})}Ts.displayName="EdgeRenderer";const Vs=n.memo(Ts),Bs=e=>`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`;function js({children:e}){const n=Oi(Bs);return t.jsx("div",{className:"react-flow__viewport xyflow__viewport react-flow__container",style:{transform:n},children:e})}const Hs=e=>e.panZoom?.syncViewport;function Zs(e){return e.connection.inProgress?{...e.connection,to:Xo(e.connection.to,e.transform)}:{...e.connection}}function Xs(e){const t=function(e){if(e)return t=>{const n=Zs(t);return e(n)};return Zs}(e);return Oi(t,Xi)}const Ys=e=>({nodesConnectable:e.nodesConnectable,isValid:e.connection.isValid,inProgress:e.connection.inProgress,width:e.width,height:e.height});function Fs({containerStyle:e,style:n,type:o,component:i}){const{nodesConnectable:a,width:s,height:l,isValid:c,inProgress:u}=Oi(Ys,Xi);return!!(s&&a&&u)?t.jsx("svg",{style:e,width:s,height:l,className:"react-flow__connectionline react-flow__container",children:t.jsx("g",{className:r(["react-flow__connection",vo(c)]),children:t.jsx(Ws,{style:n,type:o,CustomComponent:i,isValid:c})})}):null}const Ws=({style:n,type:o=e.ConnectionLineType.Bezier,CustomComponent:r,isValid:i})=>{const{inProgress:a,from:s,fromNode:l,fromHandle:c,fromPosition:u,to:d,toNode:h,toHandle:f,toPosition:g}=Xs();if(!a)return;if(r)return t.jsx(r,{connectionLineType:o,connectionLineStyle:n,fromNode:l,fromHandle:c,fromX:s.x,fromY:s.y,toX:d.x,toY:d.y,fromPosition:u,toPosition:g,connectionStatus:vo(i),toNode:h,toHandle:f});let p="";const m={sourceX:s.x,sourceY:s.y,sourcePosition:u,targetX:d.x,targetY:d.y,targetPosition:g};switch(o){case e.ConnectionLineType.Bezier:[p]=dr(m);break;case e.ConnectionLineType.SimpleBezier:[p]=fs(m);break;case e.ConnectionLineType.Step:[p]=wr({...m,borderRadius:0});break;case e.ConnectionLineType.SmoothStep:[p]=wr(m);break;default:[p]=mr(m)}return t.jsx("path",{d:p,fill:"none",className:"react-flow__connection-path",style:n})};Ws.displayName="ConnectionLine";const Ks={};function Gs(e=Ks){n.useRef(e),Ai(),n.useEffect((()=>{}),[e])}function qs({nodeTypes:e,edgeTypes:o,onInit:r,onNodeClick:i,onEdgeClick:a,onNodeDoubleClick:s,onEdgeDoubleClick:l,onNodeMouseEnter:c,onNodeMouseMove:u,onNodeMouseLeave:d,onNodeContextMenu:h,onSelectionContextMenu:f,onSelectionStart:g,onSelectionEnd:p,connectionLineType:m,connectionLineStyle:y,connectionLineComponent:v,connectionLineContainerStyle:x,selectionKeyCode:w,selectionOnDrag:b,selectionMode:S,multiSelectionKeyCode:C,panActivationKeyCode:E,zoomActivationKeyCode:k,deleteKeyCode:M,onlyRenderVisibleElements:N,elementsSelectable:_,defaultViewport:P,translateExtent:z,minZoom:O,maxZoom:A,preventScrolling:D,defaultMarkerColor:I,zoomOnScroll:R,zoomOnPinch:L,panOnScroll:$,panOnScrollSpeed:T,panOnScrollMode:V,zoomOnDoubleClick:B,panOnDrag:j,onPaneClick:H,onPaneMouseEnter:Z,onPaneMouseMove:X,onPaneMouseLeave:Y,onPaneScroll:F,onPaneContextMenu:W,paneClickDistance:K,nodeClickDistance:G,onEdgeContextMenu:q,onEdgeMouseEnter:U,onEdgeMouseMove:Q,onEdgeMouseLeave:J,reconnectRadius:ee,onReconnect:te,onReconnectStart:ne,onReconnectEnd:oe,noDragClassName:re,noWheelClassName:ie,noPanClassName:ae,disableKeyboardA11y:se,nodeExtent:le,rfId:ce,viewport:ue,onViewportChange:de}){return Gs(e),Gs(o),Ai(),n.useRef(!1),n.useEffect((()=>{}),[]),function(e){const t=ka(),o=n.useRef(!1);n.useEffect((()=>{!o.current&&t.viewportInitialized&&e&&(setTimeout((()=>e(t)),1),o.current=!0)}),[e,t.viewportInitialized])}(r),function(e){const t=Oi(Hs),o=Ai();n.useEffect((()=>{e&&(t?.(e),o.setState({transform:[e.x,e.y,e.zoom]}))}),[e,t])}(ue),t.jsx(Qa,{onPaneClick:H,onPaneMouseEnter:Z,onPaneMouseMove:X,onPaneMouseLeave:Y,onPaneContextMenu:W,onPaneScroll:F,paneClickDistance:K,deleteKeyCode:M,selectionKeyCode:w,selectionOnDrag:b,selectionMode:S,onSelectionStart:g,onSelectionEnd:p,multiSelectionKeyCode:C,panActivationKeyCode:E,zoomActivationKeyCode:k,elementsSelectable:_,zoomOnScroll:R,zoomOnPinch:L,zoomOnDoubleClick:B,panOnScroll:$,panOnScrollSpeed:T,panOnScrollMode:V,panOnDrag:j,defaultViewport:P,translateExtent:z,minZoom:O,maxZoom:A,onSelectionContextMenu:f,preventScrolling:D,noDragClassName:re,noWheelClassName:ie,noPanClassName:ae,disableKeyboardA11y:se,onViewportChange:de,isControlledViewport:!!ue,children:t.jsxs(js,{children:[t.jsx(Vs,{edgeTypes:o,onEdgeClick:a,onEdgeDoubleClick:l,onReconnect:te,onReconnectStart:ne,onReconnectEnd:oe,onlyRenderVisibleElements:N,onEdgeContextMenu:q,onEdgeMouseEnter:U,onEdgeMouseMove:Q,onEdgeMouseLeave:J,reconnectRadius:ee,defaultMarkerColor:I,noPanClassName:ae,disableKeyboardA11y:se,rfId:ce}),t.jsx(Fs,{style:y,type:m,component:v,containerStyle:x}),t.jsx("div",{className:"react-flow__edgelabel-renderer"}),t.jsx(rs,{nodeTypes:e,onNodeClick:i,onNodeDoubleClick:s,onNodeMouseEnter:c,onNodeMouseMove:u,onNodeMouseLeave:d,onNodeContextMenu:h,nodeClickDistance:G,onlyRenderVisibleElements:N,noPanClassName:ae,noDragClassName:re,disableKeyboardA11y:se,nodeExtent:le,rfId:ce}),t.jsx("div",{className:"react-flow__viewport-portal"})]})})}qs.displayName="GraphView";const Us=n.memo(qs),Qs=({nodes:t,edges:n,defaultNodes:o,defaultEdges:r,width:i,height:a,fitView:s,fitViewOptions:l,minZoom:c=.5,maxZoom:u=2,nodeOrigin:d,nodeExtent:h}={})=>{const f=new Map,g=new Map,p=new Map,m=new Map,y=r??n??[],v=o??t??[],x=d??[0,0],w=h??ro;Rr(p,m,y);const b=zr(v,f,g,{nodeOrigin:x,nodeExtent:w,elevateNodesOnSelect:!1});let S=[0,0,1];if(s&&i&&a){const e=Co(f,{filter:e=>!(!e.width&&!e.initialWidth||!e.height&&!e.initialHeight)}),{x:t,y:n,zoom:o}=Wo(e,i,a,c,u,l?.padding??.1);S=[t,n,o]}return{rfId:"1",width:0,height:0,transform:S,nodes:v,nodesInitialized:b,nodeLookup:f,parentLookup:g,edges:y,edgeLookup:m,connectionLookup:p,onNodesChange:null,onEdgesChange:null,hasDefaultNodes:void 0!==o,hasDefaultEdges:void 0!==r,panZoom:null,minZoom:c,maxZoom:u,translateExtent:ro,nodeExtent:w,nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,connectionMode:e.ConnectionMode.Strict,domNode:null,paneDragging:!1,noPanClassName:"nopan",nodeOrigin:x,nodeDragThreshold:1,connectionDragThreshold:1,snapGrid:[15,15],snapToGrid:!1,nodesDraggable:!0,nodesConnectable:!0,nodesFocusable:!0,edgesFocusable:!0,edgesReconnectable:!0,elementsSelectable:!0,elevateNodesOnSelect:!0,elevateEdgesOnSelect:!1,selectNodesOnDrag:!0,multiSelectionActive:!1,fitViewQueued:s??!1,fitViewOptions:l,fitViewResolver:null,connection:{...uo},connectionClickStartHandle:null,connectOnClick:!0,ariaLiveMessage:"",autoPanOnConnect:!0,autoPanOnNodeDrag:!0,autoPanOnNodeFocus:!0,autoPanSpeed:15,connectionRadius:20,onError:Ho,isValidConnection:void 0,onSelectionChangeHandlers:[],lib:"react",debug:!1,ariaLabelConfig:ao}},Js=({nodes:e,edges:t,defaultNodes:n,defaultEdges:o,width:r,height:i,fitView:a,fitViewOptions:s,minZoom:l,maxZoom:c,nodeOrigin:u,nodeExtent:d})=>{return h=(h,f)=>{async function g(){const{nodeLookup:e,panZoom:t,fitViewOptions:n,fitViewResolver:o,width:r,height:i,minZoom:a,maxZoom:s}=f();t&&(await Mo({nodes:e,width:r,height:i,panZoom:t,minZoom:a,maxZoom:s},n),o?.resolve(!0),h({fitViewResolver:null}))}return{...Qs({nodes:e,edges:t,width:r,height:i,fitView:a,fitViewOptions:s,minZoom:l,maxZoom:c,nodeOrigin:u,nodeExtent:d,defaultNodes:n,defaultEdges:o}),setNodes:e=>{const{nodeLookup:t,parentLookup:n,nodeOrigin:o,elevateNodesOnSelect:r,fitViewQueued:i}=f(),a=zr(e,t,n,{nodeOrigin:o,nodeExtent:d,elevateNodesOnSelect:r,checkEquality:!0});i&&a?(g(),h({nodes:e,nodesInitialized:a,fitViewQueued:!1,fitViewOptions:void 0})):h({nodes:e,nodesInitialized:a})},setEdges:e=>{const{connectionLookup:t,edgeLookup:n}=f();Rr(t,n,e),h({edges:e})},setDefaultNodesAndEdges:(e,t)=>{if(e){const{setNodes:t}=f();t(e),h({hasDefaultNodes:!0})}if(t){const{setEdges:e}=f();e(t),h({hasDefaultEdges:!0})}},updateNodeInternals:e=>{const{triggerNodeChanges:t,nodeLookup:n,parentLookup:o,domNode:r,nodeOrigin:i,nodeExtent:a,debug:s,fitViewQueued:l}=f(),{changes:c,updatedInternals:u}=function(e,t,n,o,r,i){const a=o?.querySelector(".xyflow__viewport");let s=!1;if(!a)return{changes:[],updatedInternals:s};const l=[],c=window.getComputedStyle(a),{m22:u}=new window.DOMMatrixReadOnly(c.transform),d=[];for(const o of e.values()){const e=t.get(o.id);if(!e)continue;if(e.hidden){t.set(e.id,{...e,internals:{...e.internals,handleBounds:void 0}}),s=!0;continue}const a=tr(o.nodeElement),c=e.measured.width!==a.width||e.measured.height!==a.height;if(a.width&&a.height&&(c||!e.internals.handleBounds||o.force)){const h=o.nodeElement.getBoundingClientRect(),f=Go(e.extent)?e.extent:i;let{positionAbsolute:g}=e.internals;e.parentId&&"parent"===e.extent?g=zo(g,a,t.get(e.parentId)):f&&(g=Po(g,f,a));const p={...e,measured:a,internals:{...e.internals,positionAbsolute:g,handleBounds:{source:sr("source",o.nodeElement,h,u,e.id),target:sr("target",o.nodeElement,h,u,e.id)}}};t.set(e.id,p),e.parentId&&Or(p,t,n,{nodeOrigin:r}),s=!0,c&&(l.push({id:e.id,type:"dimensions",dimensions:a}),e.expandParent&&e.parentId&&d.push({id:e.id,parentId:e.parentId,rect:Lo(p,r)}))}}if(d.length>0){const e=Dr(d,t,n,r);l.push(...e)}return{changes:l,updatedInternals:s}}(e,n,o,r,i,a);u&&(function(e,t,n){const o=Pr(Nr,n);for(const n of e.values())if(n.parentId)Or(n,e,t,o);else{const e=bo(n,o.nodeOrigin),t=Go(n.extent)?n.extent:o.nodeExtent,r=Po(e,t,qo(n));n.internals.positionAbsolute=r}}(n,o,{nodeOrigin:i,nodeExtent:a}),l?(g(),h({fitViewQueued:!1,fitViewOptions:void 0})):h({}),c?.length>0&&(s&&console.log("React Flow: trigger node changes",c),t?.(c)))},updateNodePositions:(e,t=!1)=>{const n=[],o=[],{nodeLookup:r,triggerNodeChanges:i}=f();for(const[i,a]of e){const e=r.get(i),s=!!(e?.expandParent&&e?.parentId&&a?.position),l={id:i,type:"position",position:s?{x:Math.max(0,a.position.x),y:Math.max(0,a.position.y)}:a.position,dragging:t};s&&e.parentId&&n.push({id:i,parentId:e.parentId,rect:{...a.internals.positionAbsolute,width:a.measured.width??0,height:a.measured.height??0}}),o.push(l)}if(n.length>0){const{parentLookup:e,nodeOrigin:t}=f(),i=Dr(n,r,e,t);o.push(...i)}i(o)},triggerNodeChanges:e=>{const{onNodesChange:t,setNodes:n,nodes:o,hasDefaultNodes:r,debug:i}=f();e?.length&&(r&&n(da(e,o)),i&&console.log("React Flow: trigger node changes",e),t?.(e))},triggerEdgeChanges:e=>{const{onEdgesChange:t,setEdges:n,edges:o,hasDefaultEdges:r,debug:i}=f();e?.length&&(r&&n(ha(e,o)),i&&console.log("React Flow: trigger edge changes",e),t?.(e))},addSelectedNodes:e=>{const{multiSelectionActive:t,edgeLookup:n,nodeLookup:o,triggerNodeChanges:r,triggerEdgeChanges:i}=f();t?r(e.map((e=>fa(e,!0)))):(r(ga(o,new Set([...e]),!0)),i(ga(n)))},addSelectedEdges:e=>{const{multiSelectionActive:t,edgeLookup:n,nodeLookup:o,triggerNodeChanges:r,triggerEdgeChanges:i}=f();t?i(e.map((e=>fa(e,!0)))):(i(ga(n,new Set([...e]))),r(ga(o,new Set,!0)))},unselectNodesAndEdges:({nodes:e,edges:t}={})=>{const{edges:n,nodes:o,nodeLookup:r,triggerNodeChanges:i,triggerEdgeChanges:a}=f(),s=t||n,l=(e||o).map((e=>{const t=r.get(e.id);return t&&(t.selected=!1),fa(e.id,!1)})),c=s.map((e=>fa(e.id,!1)));i(l),a(c)},setMinZoom:e=>{const{panZoom:t,maxZoom:n}=f();t?.setScaleExtent([e,n]),h({minZoom:e})},setMaxZoom:e=>{const{panZoom:t,minZoom:n}=f();t?.setScaleExtent([n,e]),h({maxZoom:e})},setTranslateExtent:e=>{f().panZoom?.setTranslateExtent(e),h({translateExtent:e})},setPaneClickDistance:e=>{f().panZoom?.setClickDistance(e)},resetSelectedElements:()=>{const{edges:e,nodes:t,triggerNodeChanges:n,triggerEdgeChanges:o,elementsSelectable:r}=f();if(!r)return;const i=t.reduce(((e,t)=>t.selected?[...e,fa(t.id,!1)]:e),[]),a=e.reduce(((e,t)=>t.selected?[...e,fa(t.id,!1)]:e),[]);n(i),o(a)},setNodeExtent:e=>{const{nodes:t,nodeLookup:n,parentLookup:o,nodeOrigin:r,elevateNodesOnSelect:i,nodeExtent:a}=f();e[0][0]===a[0][0]&&e[0][1]===a[0][1]&&e[1][0]===a[1][0]&&e[1][1]===a[1][1]||(zr(t,n,o,{nodeOrigin:r,nodeExtent:e,elevateNodesOnSelect:i,checkEquality:!1}),h({nodeExtent:e}))},panBy:e=>{const{transform:t,width:n,height:o,panZoom:r,translateExtent:i}=f();return async function({delta:e,panZoom:t,transform:n,translateExtent:o,width:r,height:i}){if(!t||!e.x&&!e.y)return Promise.resolve(!1);const a=await t.setViewportConstrained({x:n[0]+e.x,y:n[1]+e.y,zoom:n[2]},[[0,0],[r,i]],o),s=!!a&&(a.x!==n[0]||a.y!==n[1]||a.k!==n[2]);return Promise.resolve(s)}({delta:e,panZoom:r,transform:t,translateExtent:i,width:n,height:o})},setCenter:async(e,t,n)=>{const{width:o,height:r,maxZoom:i,panZoom:a}=f();if(!a)return Promise.resolve(!1);const s=void 0!==n?.zoom?n.zoom:i;return await a.setViewport({x:o/2-e*s,y:r/2-t*s,zoom:s},{duration:n?.duration,ease:n?.ease,interpolate:n?.interpolate}),Promise.resolve(!0)},cancelConnection:()=>{h({connection:{...uo}})},updateConnection:e=>{h({connection:e})},reset:()=>h({...Qs()})}},f=Object.is,h?Ni(h,f):Ni;var h,f};function el({initialNodes:e,initialEdges:o,defaultNodes:r,defaultEdges:i,initialWidth:a,initialHeight:s,initialMinZoom:l,initialMaxZoom:c,initialFitViewOptions:u,fitView:d,nodeOrigin:h,nodeExtent:f,children:g}){const[p]=n.useState((()=>Js({nodes:e,edges:o,defaultNodes:r,defaultEdges:i,width:a,height:s,fitView:d,minZoom:l,maxZoom:c,fitViewOptions:u,nodeOrigin:h,nodeExtent:f})));return t.jsx(Pi,{value:p,children:t.jsx(Ca,{children:g})})}function tl({children:e,nodes:o,edges:r,defaultNodes:i,defaultEdges:a,width:s,height:l,fitView:c,fitViewOptions:u,minZoom:d,maxZoom:h,nodeOrigin:f,nodeExtent:g}){return n.useContext(_i)?t.jsx(t.Fragment,{children:e}):t.jsx(el,{initialNodes:o,initialEdges:r,defaultNodes:i,defaultEdges:a,initialWidth:s,initialHeight:l,fitView:c,initialFitViewOptions:u,initialMinZoom:d,initialMaxZoom:h,nodeOrigin:f,nodeExtent:g,children:e})}const nl={width:"100%",height:"100%",overflow:"hidden",position:"relative",zIndex:0};var ol=xa((function({nodes:o,edges:i,defaultNodes:a,defaultEdges:s,className:l,nodeTypes:c,edgeTypes:u,onNodeClick:d,onEdgeClick:h,onInit:f,onMove:g,onMoveStart:p,onMoveEnd:m,onConnect:y,onConnectStart:v,onConnectEnd:x,onClickConnectStart:w,onClickConnectEnd:b,onNodeMouseEnter:S,onNodeMouseMove:C,onNodeMouseLeave:E,onNodeContextMenu:k,onNodeDoubleClick:M,onNodeDragStart:N,onNodeDrag:_,onNodeDragStop:P,onNodesDelete:z,onEdgesDelete:O,onDelete:A,onSelectionChange:D,onSelectionDragStart:I,onSelectionDrag:R,onSelectionDragStop:L,onSelectionContextMenu:$,onSelectionStart:T,onSelectionEnd:V,onBeforeDelete:B,connectionMode:j,connectionLineType:H=e.ConnectionLineType.Bezier,connectionLineStyle:Z,connectionLineComponent:X,connectionLineContainerStyle:Y,deleteKeyCode:F="Backspace",selectionKeyCode:W="Shift",selectionOnDrag:K=!1,selectionMode:G=e.SelectionMode.Full,panActivationKeyCode:q="Space",multiSelectionKeyCode:U=(Ko()?"Meta":"Control"),zoomActivationKeyCode:Q=(Ko()?"Meta":"Control"),snapToGrid:J,snapGrid:ee,onlyRenderVisibleElements:te=!1,selectNodesOnDrag:ne,nodesDraggable:oe,autoPanOnNodeFocus:re,nodesConnectable:ie,nodesFocusable:ae,nodeOrigin:se=Ui,edgesFocusable:le,edgesReconnectable:ce,elementsSelectable:ue=!0,defaultViewport:de=Qi,minZoom:he=.5,maxZoom:fe=2,translateExtent:ge=ro,preventScrolling:pe=!0,nodeExtent:me,defaultMarkerColor:ye="#b1b1b7",zoomOnScroll:ve=!0,zoomOnPinch:xe=!0,panOnScroll:we=!1,panOnScrollSpeed:be=.5,panOnScrollMode:Se=e.PanOnScrollMode.Free,zoomOnDoubleClick:Ce=!0,panOnDrag:Ee=!0,onPaneClick:ke,onPaneMouseEnter:Me,onPaneMouseMove:Ne,onPaneMouseLeave:_e,onPaneScroll:Pe,onPaneContextMenu:ze,paneClickDistance:Oe=0,nodeClickDistance:Ae=0,children:De,onReconnect:Ie,onReconnectStart:Re,onReconnectEnd:Le,onEdgeContextMenu:$e,onEdgeDoubleClick:Te,onEdgeMouseEnter:Ve,onEdgeMouseMove:Be,onEdgeMouseLeave:je,reconnectRadius:He=10,onNodesChange:Ze,onEdgesChange:Xe,noDragClassName:Ye="nodrag",noWheelClassName:Fe="nowheel",noPanClassName:We="nopan",fitView:Ke,fitViewOptions:Ge,connectOnClick:qe,attributionPosition:Ue,proOptions:Qe,defaultEdgeOptions:Je,elevateNodesOnSelect:et,elevateEdgesOnSelect:tt,disableKeyboardA11y:nt=!1,autoPanOnConnect:ot,autoPanOnNodeDrag:rt,autoPanSpeed:it,connectionRadius:at,isValidConnection:st,onError:lt,style:ct,id:ut,nodeDragThreshold:dt,connectionDragThreshold:ht,viewport:ft,onViewportChange:gt,width:pt,height:mt,colorMode:yt="light",debug:vt,onScroll:xt,ariaLabelConfig:wt,...bt},St){const Ct=ut||"1",Et=function(e){const[t,o]=n.useState("system"===e?null:e);return n.useEffect((()=>{if("system"!==e)return void o(e);const t=oa(),n=()=>o(t?.matches?"dark":"light");return n(),t?.addEventListener("change",n),()=>{t?.removeEventListener("change",n)}}),[e]),null!==t?t:oa()?.matches?"dark":"light"}(yt),kt=n.useCallback((e=>{e.currentTarget.scrollTo({top:0,left:0,behavior:"instant"}),xt?.(e)}),[xt]);return t.jsx("div",{"data-testid":"rf__wrapper",...bt,onScroll:kt,style:{...ct,...nl},ref:St,className:r(["react-flow",l,Et]),id:ut,role:"application",children:t.jsxs(tl,{nodes:o,edges:i,width:pt,height:mt,fitView:Ke,fitViewOptions:Ge,minZoom:he,maxZoom:fe,nodeOrigin:se,nodeExtent:me,children:[t.jsx(Us,{onInit:f,onNodeClick:d,onEdgeClick:h,onNodeMouseEnter:S,onNodeMouseMove:C,onNodeMouseLeave:E,onNodeContextMenu:k,onNodeDoubleClick:M,nodeTypes:c,edgeTypes:u,connectionLineType:H,connectionLineStyle:Z,connectionLineComponent:X,connectionLineContainerStyle:Y,selectionKeyCode:W,selectionOnDrag:K,selectionMode:G,deleteKeyCode:F,multiSelectionKeyCode:U,panActivationKeyCode:q,zoomActivationKeyCode:Q,onlyRenderVisibleElements:te,defaultViewport:de,translateExtent:ge,minZoom:he,maxZoom:fe,preventScrolling:pe,zoomOnScroll:ve,zoomOnPinch:xe,zoomOnDoubleClick:Ce,panOnScroll:we,panOnScrollSpeed:be,panOnScrollMode:Se,panOnDrag:Ee,onPaneClick:ke,onPaneMouseEnter:Me,onPaneMouseMove:Ne,onPaneMouseLeave:_e,onPaneScroll:Pe,onPaneContextMenu:ze,paneClickDistance:Oe,nodeClickDistance:Ae,onSelectionContextMenu:$,onSelectionStart:T,onSelectionEnd:V,onReconnect:Ie,onReconnectStart:Re,onReconnectEnd:Le,onEdgeContextMenu:$e,onEdgeDoubleClick:Te,onEdgeMouseEnter:Ve,onEdgeMouseMove:Be,onEdgeMouseLeave:je,reconnectRadius:He,defaultMarkerColor:ye,noDragClassName:Ye,noWheelClassName:Fe,noPanClassName:We,rfId:Ct,disableKeyboardA11y:nt,nodeExtent:me,viewport:ft,onViewportChange:gt}),t.jsx(na,{nodes:o,edges:i,defaultNodes:a,defaultEdges:s,onConnect:y,onConnectStart:v,onConnectEnd:x,onClickConnectStart:w,onClickConnectEnd:b,nodesDraggable:oe,autoPanOnNodeFocus:re,nodesConnectable:ie,nodesFocusable:ae,edgesFocusable:le,edgesReconnectable:ce,elementsSelectable:ue,elevateNodesOnSelect:et,elevateEdgesOnSelect:tt,minZoom:he,maxZoom:fe,nodeExtent:me,onNodesChange:Ze,onEdgesChange:Xe,snapToGrid:J,snapGrid:ee,connectionMode:j,translateExtent:ge,connectOnClick:qe,defaultEdgeOptions:Je,fitView:Ke,fitViewOptions:Ge,onNodesDelete:z,onEdgesDelete:O,onDelete:A,onNodeDragStart:N,onNodeDrag:_,onNodeDragStop:P,onSelectionDrag:R,onSelectionDragStart:I,onSelectionDragStop:L,onMove:g,onMoveStart:p,onMoveEnd:m,noPanClassName:We,nodeOrigin:se,rfId:Ct,autoPanOnConnect:ot,autoPanOnNodeDrag:rt,autoPanSpeed:it,onError:lt,connectionRadius:at,isValidConnection:st,selectNodesOnDrag:ne,nodeDragThreshold:dt,connectionDragThreshold:ht,onBeforeDelete:B,paneClickDistance:Oe,debug:vt,ariaLabelConfig:wt}),t.jsx(qi,{onSelectionChange:D}),De,t.jsx(Zi,{proOptions:Qe,position:Ue}),t.jsx(ji,{rfId:Ct,disableKeyboardA11y:nt})]})})}));const rl=e=>e.domNode?.querySelector(".react-flow__edgelabel-renderer");const il=e=>e.domNode?.querySelector(".react-flow__viewport-portal");const al=e=>e.nodes;const sl=e=>e.edges;const ll=e=>({x:e.transform[0],y:e.transform[1],zoom:e.transform[2]});const cl=oo.error014();function ul({dimensions:e,lineWidth:n,variant:o,className:i}){return t.jsx("path",{strokeWidth:n,d:`M${e[0]/2} 0 V${e[1]} M0 ${e[1]/2} H${e[0]}`,className:r(["react-flow__background-pattern",o,i])})}function dl({radius:e,className:n}){return t.jsx("circle",{cx:e,cy:e,r:e,className:r(["react-flow__background-pattern","dots",n])})}var hl;e.BackgroundVariant=void 0,(hl=e.BackgroundVariant||(e.BackgroundVariant={})).Lines="lines",hl.Dots="dots",hl.Cross="cross";const fl={[e.BackgroundVariant.Dots]:1,[e.BackgroundVariant.Lines]:1,[e.BackgroundVariant.Cross]:6},gl=e=>({transform:e.transform,patternId:`pattern-${e.rfId}`});function pl({id:o,variant:i=e.BackgroundVariant.Dots,gap:a=20,size:s,lineWidth:l=1,offset:c=0,color:u,bgColor:d,style:h,className:f,patternClassName:g}){const p=n.useRef(null),{transform:m,patternId:y}=Oi(gl,Xi),v=s||fl[i],x=i===e.BackgroundVariant.Dots,w=i===e.BackgroundVariant.Cross,b=Array.isArray(a)?a:[a,a],S=[b[0]*m[2]||1,b[1]*m[2]||1],C=v*m[2],E=Array.isArray(c)?c:[c,c],k=w?[C,C]:S,M=[E[0]*m[2]||1+k[0]/2,E[1]*m[2]||1+k[1]/2],N=`${y}${o||""}`;return t.jsxs("svg",{className:r(["react-flow__background",f]),style:{...h,..._a,"--xy-background-color-props":d,"--xy-background-pattern-color-props":u},ref:p,"data-testid":"rf__background",children:[t.jsx("pattern",{id:N,x:m[0]%S[0],y:m[1]%S[1],width:S[0],height:S[1],patternUnits:"userSpaceOnUse",patternTransform:`translate(-${M[0]},-${M[1]})`,children:x?t.jsx(dl,{radius:C/2,className:g}):t.jsx(ul,{dimensions:k,lineWidth:l,variant:i,className:g})}),t.jsx("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:`url(#${N})`})]})}pl.displayName="Background";const ml=n.memo(pl);function yl(){return t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",children:t.jsx("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"})})}function vl(){return t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5",children:t.jsx("path",{d:"M0 0h32v4.2H0z"})})}function xl(){return t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30",children:t.jsx("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"})})}function wl(){return t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32",children:t.jsx("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"})})}function bl(){return t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32",children:t.jsx("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"})})}function Sl({children:e,className:n,...o}){return t.jsx("button",{type:"button",className:r(["react-flow__controls-button",n]),...o,children:e})}const Cl=e=>({isInteractive:e.nodesDraggable||e.nodesConnectable||e.elementsSelectable,minZoomReached:e.transform[2]<=e.minZoom,maxZoomReached:e.transform[2]>=e.maxZoom,ariaLabelConfig:e.ariaLabelConfig});function El({style:e,showZoom:n=!0,showFitView:o=!0,showInteractive:i=!0,fitViewOptions:a,onZoomIn:s,onZoomOut:l,onFitView:c,onInteractiveChange:u,className:d,children:h,position:f="bottom-left",orientation:g="vertical","aria-label":p}){const m=Ai(),{isInteractive:y,minZoomReached:v,maxZoomReached:x,ariaLabelConfig:w}=Oi(Cl,Xi),{zoomIn:b,zoomOut:S,fitView:C}=ka(),E="horizontal"===g?"horizontal":"vertical";return t.jsxs(Hi,{className:r(["react-flow__controls",E,d]),position:f,style:e,"data-testid":"rf__controls","aria-label":p??w["controls.ariaLabel"],children:[n&&t.jsxs(t.Fragment,{children:[t.jsx(Sl,{onClick:()=>{b(),s?.()},className:"react-flow__controls-zoomin",title:w["controls.zoomIn.ariaLabel"],"aria-label":w["controls.zoomIn.ariaLabel"],disabled:x,children:t.jsx(yl,{})}),t.jsx(Sl,{onClick:()=>{S(),l?.()},className:"react-flow__controls-zoomout",title:w["controls.zoomOut.ariaLabel"],"aria-label":w["controls.zoomOut.ariaLabel"],disabled:v,children:t.jsx(vl,{})})]}),o&&t.jsx(Sl,{className:"react-flow__controls-fitview",onClick:()=>{C(a),c?.()},title:w["controls.fitView.ariaLabel"],"aria-label":w["controls.fitView.ariaLabel"],children:t.jsx(xl,{})}),i&&t.jsx(Sl,{className:"react-flow__controls-interactive",onClick:()=>{m.setState({nodesDraggable:!y,nodesConnectable:!y,elementsSelectable:!y}),u?.(!y)},title:w["controls.interactive.ariaLabel"],"aria-label":w["controls.interactive.ariaLabel"],children:y?t.jsx(bl,{}):t.jsx(wl,{})}),h]})}El.displayName="Controls";const kl=n.memo(El);const Ml=n.memo((function({id:e,x:n,y:o,width:i,height:a,style:s,color:l,strokeColor:c,strokeWidth:u,className:d,borderRadius:h,shapeRendering:f,selected:g,onClick:p}){const{background:m,backgroundColor:y}=s||{},v=l||m||y;return t.jsx("rect",{className:r(["react-flow__minimap-node",{selected:g},d]),x:n,y:o,rx:h,ry:h,width:i,height:a,style:{fill:v,stroke:c,strokeWidth:u},shapeRendering:f,onClick:p?t=>p(t,e):void 0})})),Nl=e=>e.nodes.map((e=>e.id)),_l=e=>e instanceof Function?e:()=>e;const Pl=n.memo((function({id:e,nodeColorFunc:n,nodeStrokeColorFunc:o,nodeClassNameFunc:r,nodeBorderRadius:i,nodeStrokeWidth:a,shapeRendering:s,NodeComponent:l,onClick:c}){const{node:u,x:d,y:h,width:f,height:g}=Oi((t=>{const{internals:n}=t.nodeLookup.get(e),o=n.userNode,{x:r,y:i}=n.positionAbsolute,{width:a,height:s}=qo(o);return{node:o,x:r,y:i,width:a,height:s}}),Xi);return u&&!u.hidden&&Uo(u)?t.jsx(l,{x:d,y:h,width:f,height:g,style:u.style,selected:!!u.selected,className:r(u),color:n(u),borderRadius:i,strokeColor:o(u),strokeWidth:a,shapeRendering:s,onClick:c,id:u.id}):null}));var zl=n.memo((function({nodeStrokeColor:e,nodeColor:n,nodeClassName:o="",nodeBorderRadius:r=5,nodeStrokeWidth:i,nodeComponent:a=Ml,onClick:s}){const l=Oi(Nl,Xi),c=_l(n),u=_l(e),d=_l(o),h="undefined"==typeof window||window.chrome?"crispEdges":"geometricPrecision";return t.jsx(t.Fragment,{children:l.map((e=>t.jsx(Pl,{id:e,nodeColorFunc:c,nodeStrokeColorFunc:u,nodeClassNameFunc:d,nodeBorderRadius:r,nodeStrokeWidth:i,NodeComponent:a,onClick:s,shapeRendering:h},e)))})}));const Ol=e=>!e.hidden,Al=e=>{const t={x:-e.transform[0]/e.transform[2],y:-e.transform[1]/e.transform[2],width:e.width/e.transform[2],height:e.height/e.transform[2]};return{viewBB:t,boundingRect:e.nodeLookup.size>0?To(Co(e.nodeLookup,{filter:Ol}),t):t,rfId:e.rfId,panZoom:e.panZoom,translateExtent:e.translateExtent,flowWidth:e.width,flowHeight:e.height,ariaLabelConfig:e.ariaLabelConfig}};function Dl({style:e,className:o,nodeStrokeColor:i,nodeColor:a,nodeClassName:s="",nodeBorderRadius:l=5,nodeStrokeWidth:c,nodeComponent:u,bgColor:d,maskColor:h,maskStrokeColor:f,maskStrokeWidth:g,position:p="bottom-right",onClick:m,onNodeClick:y,pannable:v=!1,zoomable:x=!1,ariaLabel:w,inversePan:b,zoomStep:S=1,offsetScale:C=5}){const E=Ai(),k=n.useRef(null),{boundingRect:M,viewBB:N,rfId:_,panZoom:P,translateExtent:z,flowWidth:O,flowHeight:A,ariaLabelConfig:D}=Oi(Al,Xi),I=e?.width??200,R=e?.height??150,L=M.width/I,$=M.height/R,T=Math.max(L,$),V=T*I,B=T*R,j=C*T,H=M.x-(V-M.width)/2-j,Z=M.y-(B-M.height)/2-j,X=V+2*j,Y=B+2*j,F=`react-flow__minimap-desc-${_}`,W=n.useRef(0),K=n.useRef();W.current=T,n.useEffect((()=>{if(k.current&&P)return K.current=function({domNode:e,panZoom:t,getTransform:n,getViewScale:o}){const r=Se(e);return{update:function({translateExtent:e,width:i,height:a,zoomStep:s=1,pannable:l=!0,zoomable:c=!0,inversePan:u=!1}){let d=[0,0];const h=no().on("start",(e=>{"mousedown"!==e.sourceEvent.type&&"touchstart"!==e.sourceEvent.type||(d=[e.sourceEvent.clientX??e.sourceEvent.touches[0].clientX,e.sourceEvent.clientY??e.sourceEvent.touches[0].clientY])})).on("zoom",l?r=>{const s=n();if("mousemove"!==r.sourceEvent.type&&"touchmove"!==r.sourceEvent.type||!t)return;const l=[r.sourceEvent.clientX??r.sourceEvent.touches[0].clientX,r.sourceEvent.clientY??r.sourceEvent.touches[0].clientY],c=[l[0]-d[0],l[1]-d[1]];d=l;const h=o()*Math.max(s[2],Math.log(s[2]))*(u?-1:1),f={x:s[0]-c[0]*h,y:s[1]-c[1]*h},g=[[0,0],[i,a]];t.setViewportConstrained({x:f.x,y:f.y,zoom:s[2]},g,e)}:null).on("zoom.wheel",c?e=>{if("wheel"!==e.sourceEvent.type||!t)return;const o=n(),r=e.sourceEvent.ctrlKey&&Ko()?10:1,i=-e.sourceEvent.deltaY*(1===e.sourceEvent.deltaMode?.05:e.sourceEvent.deltaMode?1:.002)*s,a=o[2]*Math.pow(2,i*r);t.scaleTo(a)}:null);r.call(h,{})},destroy:function(){r.on("zoom",null)},pointer:Ce}}({domNode:k.current,panZoom:P,getTransform:()=>E.getState().transform,getViewScale:()=>W.current}),()=>{K.current?.destroy()}}),[P]),n.useEffect((()=>{K.current?.update({translateExtent:z,width:O,height:A,inversePan:b,pannable:v,zoomStep:S,zoomable:x})}),[v,x,b,S,z,O,A]);const G=m?e=>{const[t,n]=K.current?.pointer(e)||[0,0];m(e,{x:t,y:n})}:void 0,q=y?n.useCallback(((e,t)=>{const n=E.getState().nodeLookup.get(t).internals.userNode;y(e,n)}),[]):void 0,U=w??D["minimap.ariaLabel"];return t.jsx(Hi,{position:p,style:{...e,"--xy-minimap-background-color-props":"string"==typeof d?d:void 0,"--xy-minimap-mask-background-color-props":"string"==typeof h?h:void 0,"--xy-minimap-mask-stroke-color-props":"string"==typeof f?f:void 0,"--xy-minimap-mask-stroke-width-props":"number"==typeof g?g*T:void 0,"--xy-minimap-node-background-color-props":"string"==typeof a?a:void 0,"--xy-minimap-node-stroke-color-props":"string"==typeof i?i:void 0,"--xy-minimap-node-stroke-width-props":"number"==typeof c?c:void 0},className:r(["react-flow__minimap",o]),"data-testid":"rf__minimap",children:t.jsxs("svg",{width:I,height:R,viewBox:`${H} ${Z} ${X} ${Y}`,className:"react-flow__minimap-svg",role:"img","aria-labelledby":F,ref:k,onClick:G,children:[U&&t.jsx("title",{id:F,children:U}),t.jsx(zl,{onClick:q,nodeColor:a,nodeStrokeColor:i,nodeBorderRadius:l,nodeClassName:s,nodeStrokeWidth:c,nodeComponent:u}),t.jsx("path",{className:"react-flow__minimap-mask",d:`M${H-j},${Z-j}h${X+2*j}v${Y+2*j}h${-X-2*j}z\n        M${N.x},${N.y}h${N.width}v${N.height}h${-N.width}z`,fillRule:"evenodd",pointerEvents:"none"})]})})}Dl.displayName="MiniMap";const Il=n.memo(Dl),Rl={[e.ResizeControlVariant.Line]:"right",[e.ResizeControlVariant.Handle]:"bottom-right"};const Ll=n.memo((function({nodeId:o,position:i,variant:a=e.ResizeControlVariant.Handle,className:s,style:l,children:c,color:u,minWidth:d=10,minHeight:h=10,maxWidth:f=Number.MAX_VALUE,maxHeight:g=Number.MAX_VALUE,keepAspectRatio:p=!1,resizeDirection:m,autoScale:y=!0,shouldResize:v,onResizeStart:x,onResize:w,onResizeEnd:b}){const S=Ha(),C="string"==typeof o?o:S,E=Ai(),k=n.useRef(null),M=a===e.ResizeControlVariant.Handle,N=Oi(n.useCallback((_=M&&y,e=>_?`${Math.max(1/e.transform[2],1)}`:void 0),[M,y]),Xi);var _;const P=n.useRef(null),z=i??Rl[a];n.useEffect((()=>{if(k.current&&C)return P.current||(P.current=fi({domNode:k.current,nodeId:C,getStoreItems:()=>{const{nodeLookup:e,transform:t,snapGrid:n,snapToGrid:o,nodeOrigin:r,domNode:i}=E.getState();return{nodeLookup:e,transform:t,snapGrid:n,snapToGrid:o,nodeOrigin:r,paneDomNode:i}},onChange:(e,t)=>{const{triggerNodeChanges:n,nodeLookup:o,parentLookup:r,nodeOrigin:i}=E.getState(),a=[],s={x:e.x,y:e.y},l=o.get(C);if(l&&l.expandParent&&l.parentId){const t=l.origin??i,n=e.width??l.measured.width??0,c=e.height??l.measured.height??0,u=Dr([{id:l.id,parentId:l.parentId,rect:{width:n,height:c,...Qo({x:e.x??l.position.x,y:e.y??l.position.y},{width:n,height:c},l.parentId,o,t)}}],o,r,i);a.push(...u),s.x=e.x?Math.max(t[0]*n,e.x):void 0,s.y=e.y?Math.max(t[1]*c,e.y):void 0}if(void 0!==s.x&&void 0!==s.y){const e={id:C,type:"position",position:{...s}};a.push(e)}if(void 0!==e.width&&void 0!==e.height){const t={id:C,type:"dimensions",resizing:!0,setAttributes:!m||("horizontal"===m?"width":"height"),dimensions:{width:e.width,height:e.height}};a.push(t)}for(const e of t){const t={...e,type:"position"};a.push(t)}n(a)},onEnd:({width:e,height:t})=>{const n={id:C,type:"dimensions",resizing:!1,dimensions:{width:e,height:t}};E.getState().triggerNodeChanges([n])}})),P.current.update({controlPosition:z,boundaries:{minWidth:d,minHeight:h,maxWidth:f,maxHeight:g},keepAspectRatio:p,resizeDirection:m,onResizeStart:x,onResize:w,onResizeEnd:b,shouldResize:v}),()=>{P.current?.destroy()}}),[z,d,h,f,g,p,x,w,b,v]);const O=z.split("-");return t.jsx("div",{className:r(["react-flow__resize-control","nodrag",...O,a,s]),ref:k,style:{...l,scale:N,...u&&{[M?"backgroundColor":"borderColor"]:u}},children:c})}));const $l=e=>e.domNode?.querySelector(".react-flow__renderer");function Tl({children:e}){const t=Oi($l);return t?o.createPortal(e,t):null}const Vl=(e,t)=>e?.internals.positionAbsolute.x!==t?.internals.positionAbsolute.x||e?.internals.positionAbsolute.y!==t?.internals.positionAbsolute.y||e?.measured.width!==t?.measured.width||e?.measured.height!==t?.measured.height||e?.selected!==t?.selected||e?.internals.z!==t?.internals.z,Bl=(e,t)=>{if(e.size!==t.size)return!1;for(const[n,o]of e)if(Vl(o,t.get(n)))return!1;return!0},jl=e=>({x:e.transform[0],y:e.transform[1],zoom:e.transform[2],selectedNodesCount:e.nodes.filter((e=>e.selected)).length});e.Background=ml,e.BaseEdge=ds,e.BezierEdge=Ns,e.ControlButton=Sl,e.Controls=kl,e.EdgeLabelRenderer=function({children:e}){const t=Oi(rl);return t?o.createPortal(e,t):null},e.EdgeText=us,e.Handle=Xa,e.MiniMap=Il,e.MiniMapNode=Ml,e.NodeResizeControl=Ll,e.NodeResizer=function({nodeId:n,isVisible:o=!0,handleClassName:r,handleStyle:i,lineClassName:a,lineStyle:s,color:l,minWidth:c=10,minHeight:u=10,maxWidth:d=Number.MAX_VALUE,maxHeight:h=Number.MAX_VALUE,keepAspectRatio:f=!1,autoScale:g=!0,shouldResize:p,onResizeStart:m,onResize:y,onResizeEnd:v}){return o?t.jsxs(t.Fragment,{children:[ii.map((o=>t.jsx(Ll,{className:a,style:s,nodeId:n,position:o,variant:e.ResizeControlVariant.Line,color:l,minWidth:c,minHeight:u,maxWidth:d,maxHeight:h,onResizeStart:m,keepAspectRatio:f,autoScale:g,shouldResize:p,onResize:y,onResizeEnd:v},o))),ri.map((e=>t.jsx(Ll,{className:r,style:i,nodeId:n,position:e,color:l,minWidth:c,minHeight:u,maxWidth:d,maxHeight:h,onResizeStart:m,keepAspectRatio:f,autoScale:g,shouldResize:p,onResize:y,onResizeEnd:v},e)))]}):null},e.NodeToolbar=function({nodeId:o,children:i,className:a,style:s,isVisible:l,position:c=e.Position.Top,offset:u=10,align:d="center",...h}){const f=Ha(),g=n.useCallback((e=>{const t=(Array.isArray(o)?o:[o||f||""]).reduce(((t,n)=>{const o=e.nodeLookup.get(n);return o&&t.set(o.id,o),t}),new Map);return t}),[o,f]),p=Oi(g,Bl),{x:m,y:y,zoom:v,selectedNodesCount:x}=Oi(jl,Xi);if(!("boolean"==typeof l?l:1===p.size&&p.values().next().value?.selected&&1===x)||!p.size)return null;const w=Co(p),b=Array.from(p.values()),S=Math.max(...b.map((e=>e.internals.z+1))),C={position:"absolute",transform:Mr(w,{x:m,y:y,zoom:v},c,u,d),zIndex:S,...s};return t.jsx(Tl,{children:t.jsx("div",{style:C,className:r(["react-flow__node-toolbar",a]),...h,"data-id":b.reduce(((e,t)=>`${e}${t.id} `),"").trim(),children:i})})},e.Panel=Hi,e.ReactFlow=ol,e.ReactFlowProvider=el,e.SimpleBezierEdge=ps,e.SmoothStepEdge=vs,e.StepEdge=bs,e.StraightEdge=Es,e.ViewportPortal=function({children:e}){const t=Oi(il);return t?o.createPortal(e,t):null},e.addEdge=pr,e.applyEdgeChanges=ha,e.applyNodeChanges=da,e.getBezierEdgeCenter=lr,e.getBezierPath=dr,e.getConnectedEdges=ko,e.getEdgeCenter=hr,e.getIncomers=(e,t,n)=>{if(!e.id)return[];const o=new Set;return n.forEach((t=>{t.target===e.id&&o.add(t.source)})),t.filter((e=>o.has(e.id)))},e.getNodesBounds=So,e.getOutgoers=(e,t,n)=>{if(!e.id)return[];const o=new Set;return n.forEach((t=>{t.source===e.id&&o.add(t.target)})),t.filter((e=>o.has(e.id)))},e.getSimpleBezierPath=fs,e.getSmoothStepPath=wr,e.getStraightPath=mr,e.getViewportForBounds=Wo,e.isEdge=va,e.isNode=ya,e.reconnectEdge=(e,t,n,o={shouldReplaceId:!0})=>{const{id:r,...i}=e;if(!t.source||!t.target)return n;if(!n.find((t=>t.id===e.id)))return n;const a={...i,id:o.shouldReplaceId?gr(t):r,source:t.source,target:t.target,sourceHandle:t.sourceHandle,targetHandle:t.targetHandle};return n.filter((e=>e.id!==r)).concat(a)},e.useConnection=Xs,e.useEdges=function(){return Oi(sl,Xi)},e.useEdgesState=function(e){const[t,o]=n.useState(e),r=n.useCallback((e=>o((t=>ha(e,t)))),[]);return[t,o,r]},e.useHandleConnections=function({type:e,id:t,nodeId:o,onConnect:r,onDisconnect:i}){console.warn("[DEPRECATED] `useHandleConnections` is deprecated. Instead use `useNodeConnections` https://reactflow.dev/api-reference/hooks/useNodeConnections");const a=Ha(),s=o??a,l=n.useRef(null),c=Oi((n=>n.connectionLookup.get(`${s}-${e}${t?`-${t}`:""}`)),mo);return n.useEffect((()=>{if(l.current&&l.current!==c){const e=c??new Map;yo(l.current,e,i),yo(e,l.current,r)}l.current=c??new Map}),[c,r,i]),n.useMemo((()=>Array.from(c?.values()??[])),[c])},e.useInternalNode=function(e){return Oi(n.useCallback((t=>t.nodeLookup.get(e)),[e]),Xi)},e.useKeyPress=ia,e.useNodeConnections=function({id:e,handleType:t,handleId:o,onConnect:r,onDisconnect:i}={}){const a=Ha(),s=e??a;if(!s)throw new Error(cl);const l=n.useRef(null),c=Oi((e=>e.connectionLookup.get(`${s}${t?o?`-${t}-${o}`:`-${t}`:""}`)),mo);return n.useEffect((()=>{if(l.current&&l.current!==c){const e=c??new Map;yo(l.current,e,i),yo(e,l.current,r)}l.current=c??new Map}),[c,r,i]),n.useMemo((()=>Array.from(c?.values()??[])),[c])},e.useNodeId=Ha,e.useNodes=function(){return Oi(al,Xi)},e.useNodesData=function(e){return Oi(n.useCallback((t=>{const n=[],o=Array.isArray(e),r=o?e:[e];for(const e of r){const o=t.nodeLookup.get(e);o&&n.push({id:o.id,type:o.type,data:o.data})}return o?n:n[0]??null}),[e]),Lr)},e.useNodesInitialized=function(e={includeHiddenNodes:!1}){return Oi((e=>t=>{if(!e.includeHiddenNodes)return t.nodesInitialized;if(0===t.nodeLookup.size)return!1;for(const[,{internals:e}]of t.nodeLookup)if(void 0===e.handleBounds||!Uo(e.userNode))return!1;return!0})(e))},e.useNodesState=function(e){const[t,o]=n.useState(e),r=n.useCallback((e=>o((t=>da(e,t)))),[]);return[t,o,r]},e.useOnSelectionChange=function({onChange:e}){const t=Ai();n.useEffect((()=>{const n=[...t.getState().onSelectionChangeHandlers,e];return t.setState({onSelectionChangeHandlers:n}),()=>{const n=t.getState().onSelectionChangeHandlers.filter((t=>t!==e));t.setState({onSelectionChangeHandlers:n})}}),[e])},e.useOnViewportChange=function({onStart:e,onChange:t,onEnd:o}){const r=Ai();n.useEffect((()=>{r.setState({onViewportChangeStart:e})}),[e]),n.useEffect((()=>{r.setState({onViewportChange:t})}),[t]),n.useEffect((()=>{r.setState({onViewportChangeEnd:o})}),[o])},e.useReactFlow=ka,e.useStore=Oi,e.useStoreApi=Ai,e.useUpdateNodeInternals=function(){const e=Ai();return n.useCallback((t=>{const{domNode:n,updateNodeInternals:o}=e.getState(),r=Array.isArray(t)?t:[t],i=new Map;r.forEach((e=>{const t=n?.querySelector(`.react-flow__node[data-id="${e}"]`);t&&i.set(e,{id:e,nodeElement:t,force:!0})})),requestAnimationFrame((()=>o(i,{triggerFitView:!1})))}),[])},e.useViewport=function(){return Oi(ll,Xi)}}));
